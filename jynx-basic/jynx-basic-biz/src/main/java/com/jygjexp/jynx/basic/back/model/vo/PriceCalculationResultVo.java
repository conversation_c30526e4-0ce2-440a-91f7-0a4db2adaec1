package com.jygjexp.jynx.basic.back.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格计算结果
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@Schema(description = "价格计算结果")
public class PriceCalculationResultVo {

    /**
     * 订单价格计算详情列表
     */
    @Schema(description = "订单价格计算详情列表")
    private List<PriceCalculationDetailVo> details;

    /**
     * 总计算成功数量
     */
    @Schema(description = "总计算成功数量")
    private Integer successCount;

    /**
     * 总计算失败数量
     */
    @Schema(description = "总计算失败数量")
    private Integer failureCount;

    /**
     * 总价格合计
     */
    @Schema(description = "总价格合计")
    private BigDecimal totalPrice;
}
