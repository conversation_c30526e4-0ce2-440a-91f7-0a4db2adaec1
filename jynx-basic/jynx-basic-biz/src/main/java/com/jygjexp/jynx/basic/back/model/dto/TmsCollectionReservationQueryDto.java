package com.jygjexp.jynx.basic.back.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "揽收预约查询参数")
public class TmsCollectionReservationQueryDto {

    // region 排序条件
    @Schema(description = "当前页")
    @NotNull
    private Long current = 1L;

    @Schema(description = "每页显示条数")
    @NotNull
    private Long size = 10L;
    // endregion

    // region 查询条件
    @Schema(description="联系人")
    private String contacts;

    @Schema(description="联系电话")
    private String contactPhone;

    @Schema(description="邮政编码")
    private String postalCode;

    @Schema(description="城市")
    private List<String> cityList;
    // endregion


}

