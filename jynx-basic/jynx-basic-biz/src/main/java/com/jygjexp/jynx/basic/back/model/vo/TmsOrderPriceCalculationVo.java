package com.jygjexp.jynx.basic.back.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 订单价格计算请求值对象
 * 专门用于价格计算的轻量级订单数据传输对象，仅包含价格计算所需的核心字段
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@Schema(description = "订单价格计算请求值对象")
public class TmsOrderPriceCalculationVo {

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Long id;

    /**
     * 客户单号/客户参考单号
     */
    @NotBlank(message = "客户单号不能为空")
    @Schema(description = "客户单号/客户参考单号")
    private String customerOrderNumber;

    /**
     * 委托单号
     */
    @NotBlank(message = "委托单号不能为空")
    @Schema(description = "委托单号")
    private String entrustedOrderNumber;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空")
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 始发地
     */
    @NotBlank(message = "始发地不能为空")
    @Schema(description = "始发地")
    private String origin;

    /**
     * 目的地
     */
    @NotBlank(message = "目的地不能为空")
    @Schema(description = "目的地")
    private String destination;

    /**
     * 发货邮编
     */
    @Schema(description = "发货邮编")
    private String shipperPostalCode;

    /**
     * 目的地邮编
     */
    @NotBlank(message = "目的地邮编不能为空")
    @Schema(description = "目的地邮编")
    private String destPostalCode;

    /**
     * 总重量(kg)
     */
    @Schema(description = "总重量(kg)")
    private BigDecimal totalWeight;

    /**
     * 总体积(m³)
     */
    @Schema(description = "总体积(m³)")
    private BigDecimal totalVolume;

}
