package com.jygjexp.jynx.basic.back.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TmsCollectionReservationVo {


    /**
     * 揽收点代码
     */
    @Schema(description="地址编码")
    private String addressCode;

    /**
     * 揽收点名称
     */
    @Schema(description="揽收点名称")
    private String collectionPointName;

    /**
     * 联系人
     */
    @Schema(description="联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @Schema(description="联系电话")
    private String contactPhone;

    /**
     * 详情地址
     */
    @Schema(description="详情地址")
    private String detailsAddress;

    /**
     * 城市
     */
    @Schema(description="城市")
    private String city;

    /**
     * 邮政编码
     */
    @Schema(description="邮政编码")
    private String postalCode;

}
