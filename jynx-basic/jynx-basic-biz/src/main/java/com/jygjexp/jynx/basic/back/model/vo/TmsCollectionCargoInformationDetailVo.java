package com.jygjexp.jynx.basic.back.model.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TmsCollectionCargoInformationDetailVo {

    // 发货人
    @Schema(description = "发货人")
    private CollectionReservation shipper;

    @Schema(description = "收货人")
    private CollectionReservation consignee;

    @Schema(description="上门起始时间")
    private LocalDateTime visitStartTime;

    @Schema(description="上门结束时间时间")
    private LocalDateTime visitEndTime;

    @Schema(description="创建时间")
    private LocalDateTime createTime;

    @Schema(description="派送时间")
    private LocalDateTime dispatchTime;

    @Schema(description="备注")
    private String remark;

    @Schema(description = "状态映射")
    private String statusStr;


    @Schema(description = "货物信息")
    private List<CollectionCargo> cargoList;


    @Schema(description = "揽收点模版")
    @Data
    public static class CollectionReservation {

        @Schema(description = "国家/省/城市")
        private String city;

        @Schema(description = "联系人电话")
        private String contactPhone;

        @Schema(description = "联系人")
        private String contacts;

        @Schema(description = "详细地址")
        private String detailsAddress;

        @Schema(description = "邮政编码")
        private String postalCode;
    }

    @Data
    public static class CollectionCargo {
        @Schema(description="主单号(客户单号)")
        @NotEmpty(message = "主单号不能为空")
        @Size(max = 50, message = "主单号长度不能超过50个字符")
        private String orderNo;

        @Schema(description = "长度")
        private BigDecimal length;

        @Schema(description = "宽度")
        private BigDecimal width;

        @Schema(description = "高度")
        private BigDecimal height;

        @Schema(description = "重量")
        private BigDecimal weight;

        @Schema(description = "数量")
        private Integer quantity;

        @Schema(description = "状态")
        private Integer status;

        @Schema(description="备注")
        private String remark;
    }

}
