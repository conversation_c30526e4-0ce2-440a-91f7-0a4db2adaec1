package com.jygjexp.jynx.basic.back.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TmsCollectionCargoInformationQueryDto {

    // region 排序条件
    @Schema(description = "当前页")
    @NotNull
    private Integer current = 1;

    @Schema(description = "每页显示条数")
    @NotNull
    private Integer size = 10;
    // endregion


    // region 筛选条件
    @Schema(description = "客户单号")
    private List<String> orderNoList;

    @Schema(description = "跟踪订单号")
    private List<String> trackNoList;

    @Schema(description = "订单状态")
    private Integer status;

    @Schema(description = "创建时间起始时间")
    private LocalDateTime startCreateTime;

    @Schema(description = "创建时间结束时间")
    private LocalDateTime endCreateTime;

    @Schema(description = "联系人")
    private String contacts;

    @Schema(description = "联系电话")
    private String contactsPhone;

    // endregion


    // region 导出条件

    @Schema(description = "导出iD")
    private List<Long> ids;

    // endregion
}
