<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbTransferBatchOrderMapper">

    <resultMap id="nbTransferBatchOrderMap" type="com.jygjexp.jynx.zxoms.entity.NbTransferBatchOrderEntity">
        <id property="boId" column="bo_id"/>
        <result property="batchId" column="batch_id"/>
        <result property="orderId" column="order_id"/>
        <result property="pickNo" column="pick_no"/>
        <result property="routeDestinationId" column="route_destination_id"/>
        <result property="memberId" column="member_id"/>
        <result property="routeId" column="route_id"/>
        <result property="optimizationProblemId" column="optimization_problem_id"/>
        <result property="sequenceNo" column="sequence_no"/>
        <result property="channelName" column="channel_name"/>
        <result property="driveTimetoNextDestination" column="drive_timeto_next_destination"/>
        <result property="trackingNumber" column="tracking_number"/>
        <result property="r4mCompleteType" column="r4m_complete_type"/>
        <result property="isLoadScan" column="is_load_scan"/>
        <result property="loadScanTime" column="load_scan_time"/>
        <result property="isLoadScan2" column="is_load_scan2"/>
        <result property="loadScanTime2" column="load_scan_time2"/>
        <result property="loadScanDriverId2" column="load_scan_driver_id2"/>
        <result property="isDriverScan" column="is_driver_scan"/>
        <result property="driverScanTime" column="driver_scan_time"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="findFirstOrderId" resultType="com.jygjexp.jynx.zxoms.nbapp.vo.TransferBatchOrderVo">
        select tbo.*, tb.batch_code, tb.order_total,tb.batch_id as TBatchId, tb.start_time from nb_transfer_batch_order tbo,
        nb_transfer_batch tb where tbo.batch_id = tb.batch_id and order_id = #{orderId} limit 1
    </select>

    <select id="listOrderCost" resultType="com.jygjexp.jynx.zxoms.vo.TransferBatchCostPageVo">
        select ifnull(sum(driver_base), 0) estimateBase,
               ifnull(sum(driver_subsidy), 0) estimateSubsidy,
               ifnull(sum(driver_weight_subsidy), 0) estimateWeightSubsidy,
               ifnull(sum(driver_base + driver_subsidy + driver_weight_subsidy), 0) estimateAmount,
               ifnull(sum(if(cost_status = 2, driver_base, 0)), 0) actualBase,
               ifnull(sum(if(cost_status = 2, driver_subsidy, 0)), 0) actualSubsidy,
               ifnull(sum(if(cost_status = 2, driver_weight_subsidy, 0)), 0) actualWeightSubsidy,
               ifnull(sum(if(cost_status = 2, driver_base, 0) + if(cost_status = 2, driver_subsidy, 0) + if(cost_status = 2, driver_weight_subsidy, 0)), 0) actualAmount
        from nb_transfer_batch_order tbo
        left join nb_order_cost oc on tbo.order_id = oc.order_id
        where tbo.batch_id = #{batchId}
    </select>

</mapper>