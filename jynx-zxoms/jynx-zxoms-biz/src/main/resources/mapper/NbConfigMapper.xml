<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbConfigMapper">

    <resultMap id="basicNbConfigMap" type="com.jygjexp.jynx.zxoms.entity.NbConfigEntity">
        <id property="configId" column="config_id"/>
        <result property="configType" column="config_type"/>
        <result property="cKey" column="c_key"/>
        <result property="cValue" column="c_value"/>
        <result property="cType" column="c_type"/>
        <result property="cDesc" column="c_desc"/>
    </resultMap>
</mapper>