<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbSmsLogMapper">

    <resultMap id="basicNbSmsLogMap" type="com.jygjexp.jynx.zxoms.entity.NbSmsLogEntity">
        <id property="logId" column="log_id"/>
        <result property="templateId" column="template_id"/>
        <result property="orderId" column="order_id"/>
        <result property="mobile" column="mobile"/>
        <result property="content" column="content"/>
        <result property="driverId" column="driver_id"/>
        <result property="sendTime" column="send_time"/>
        <result property="responseStatus" column="response_status"/>
        <result property="pathId" column="path_id"/>
        <result property="smsType" column="sms_type"/>
        <result property="smsStatus" column="sms_status"/>
        <result property="log" column="log"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>