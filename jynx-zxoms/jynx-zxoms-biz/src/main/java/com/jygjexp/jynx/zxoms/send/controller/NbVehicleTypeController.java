package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbVehicleTypeEntity;
import com.jygjexp.jynx.zxoms.send.service.NbVehicleTypeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 车辆类型
 *
 * <AUTHOR>
 * @date 2024-10-16 00:45:10
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbVehicleType" )
@Tag(description = "nbVehicleType" , name = "车辆类型管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbVehicleTypeController {

    private final  NbVehicleTypeService nbVehicleTypeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbVehicleType 车辆类型
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbVehicleType_view')" )
    public R getNbVehicleTypePage(@ParameterObject Page page, @ParameterObject NbVehicleTypeEntity nbVehicleType) {
        LambdaQueryWrapper<NbVehicleTypeEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbVehicleTypeService.page(page, wrapper));
    }


    /**
     * 通过id查询车辆类型
     * @param vehicleTypeId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{vehicleTypeId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbVehicleType_view')" )
    public R getById(@PathVariable("vehicleTypeId" ) Integer vehicleTypeId) {
        return R.ok(nbVehicleTypeService.getById(vehicleTypeId));
    }

    /**
     * 新增车辆类型
     * @param nbVehicleType 车辆类型
     * @return R
     */
    @Operation(summary = "新增车辆类型" , description = "新增车辆类型" )
    @SysLog("新增车辆类型" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbVehicleType_add')" )
    public R save(@RequestBody NbVehicleTypeEntity nbVehicleType) {
        return R.ok(nbVehicleTypeService.save(nbVehicleType));
    }

    /**
     * 修改车辆类型
     * @param nbVehicleType 车辆类型
     * @return R
     */
    @Operation(summary = "修改车辆类型" , description = "修改车辆类型" )
    @SysLog("修改车辆类型" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbVehicleType_edit')" )
    public R updateById(@RequestBody NbVehicleTypeEntity nbVehicleType) {
        return R.ok(nbVehicleTypeService.updateById(nbVehicleType));
    }

    /**
     * 通过id删除车辆类型
     * @param ids vehicleTypeId列表
     * @return R
     */
    @Operation(summary = "通过id删除车辆类型" , description = "通过id删除车辆类型" )
    @SysLog("通过id删除车辆类型" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbVehicleType_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbVehicleTypeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbVehicleType 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbVehicleType_export')" )
    public List<NbVehicleTypeEntity> export(NbVehicleTypeEntity nbVehicleType,Integer[] ids) {
        return nbVehicleTypeService.list(Wrappers.lambdaQuery(nbVehicleType).in(ArrayUtil.isNotEmpty(ids), NbVehicleTypeEntity::getVehicleTypeId, ids));
    }
}