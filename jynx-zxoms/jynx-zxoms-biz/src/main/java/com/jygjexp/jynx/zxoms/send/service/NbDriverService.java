package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface NbDriverService extends IService<NbDriverEntity> {

    IPage<NbDriverPageVo> search(Page page, NbDriverEntity entity);// 司机信息分页查询

    R auditPass(NbDriverEntity drivers);

    R auditRefuse(Integer driverId);

    R auditRefuseReason(DriverVo driverVo);

    R driverInvalid(Integer driverId);

    R driverValid(Integer driverId);

    R modifyEmail(Integer driverId, String email);

    R updateVehicle(Integer driverId);

    R checkAddDriverBefore(NbDriverEntity nbDriver);

    R processAddSucceed(NbDriverEntity nbDriver);

    R checkDriverScId();

    List<NbDriverEntity> findAuditDriver(Integer driverId);

    NbDriverEntity findBySessionId(String accessKey);   // 通过sessionID查询登录司机

    Page<NbDriverPageVo> pageListPDriverId(Page page, NbDriverEntity entity);   //查询所属父司机leader

    Page<PaisongDriverPageVo> getPaisongDriver(Page page, PaisongDriverPageVo vo);    //派送司机列表分页查询

    //app修改密码
    R UpdatePassword(String mobile,String password);

    // 上下架司机列表分页查询
    Page<NbDriverEntity> pagePutawayOrPickupName(Page page, NbDriverEntity entity);

    Boolean checkMobile(String mobile); // 校验新增司机手机号是否重复

    Boolean checkUpdateMobile(String mobile, Integer driverId); // 校验编辑司机手机号是否重复

    /**
     * 上传文件
     * @param file 文件流
     * @param dir 文件夹
     * @param groupId 分组ID
     * @param type 类型
     * @return
     */
    R uploadFile(MultipartFile file, String dir, Long groupId, String type);

    // 中大件司机导出
    List<NbDriverExcelVo> getExcel(NbDriverEntity nbDriver, Integer[] ids);

}