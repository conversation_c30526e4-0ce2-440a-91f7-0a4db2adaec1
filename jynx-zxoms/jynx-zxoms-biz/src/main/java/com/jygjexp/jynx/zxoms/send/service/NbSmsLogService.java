package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbSmsLogEntity;
import com.jygjexp.jynx.zxoms.send.vo.NbSmsLogExcelVo;
import com.jygjexp.jynx.zxoms.vo.SmsLogPageVo;

import java.util.List;

public interface NbSmsLogService extends IService<NbSmsLogEntity> {

    Page<SmsLogPageVo> search(Page page, SmsLogPageVo smsLogPageVo);

    List<NbSmsLogExcelVo> getExcel(SmsLogPageVo vo, Integer[] ids); // 短信记录导出Excel
}