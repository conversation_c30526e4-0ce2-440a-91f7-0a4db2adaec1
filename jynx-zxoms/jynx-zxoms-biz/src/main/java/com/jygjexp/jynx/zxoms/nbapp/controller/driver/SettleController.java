package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderCostDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.utils.RedisUtil;
import com.jygjexp.jynx.zxoms.send.service.NbDriverService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderCostService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.utils.NBDUtils;
import com.jygjexp.jynx.zxoms.send.utils.PathUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description: 司机账单相关
 * @Date: 2024/11/12 10:12
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/settle" )
@Tag(description = "appdriversettle" , name = "APP-司机账单相关" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SettleController extends BaseController {
    private final RedisTemplate<String, String> redisTemplate;
    private final NbOrderService orderService;
    private final NbOrderCostService orderCostService;
    private final NbSortingCenterService sortingCenterService;
    private final NbDriverService driverService;

    @Operation(summary = "需要密码" , description = "需要密码" )
    @PostMapping("/requiredPwd" )
    public void requiredPwd() {
        NbDriverEntity driver = getLoginDriver();
        String key = "nb_driver_settle_show_" + driver.getDriverId();

        Object obj = redisTemplate.opsForValue().get(key);
        if (obj == null) {
            renderAppErr("1", "need pwd");
        } else {
            redisTemplate.expire(key, Duration.ofDays(RedisUtil.MINUTE_10));
            renderAppSuc();
        }
    }

    @Operation(summary = "校验密码" , description = "校验密码")
    @PostMapping("/checkPwd")
    public void checkPwd(@RequestParam("settlePwd") String settlePwd) {
        NbDriverEntity driver = getLoginDriver();
        if (driver.getSettlePassword().equals(settlePwd)) {
            renderAppSuc();
            String key = "nb_driver_settle_show_" + driver.getDriverId();
            String formattedDate = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss");
            redisTemplate.opsForValue().set(key, formattedDate, 10, TimeUnit.MINUTES);
            return;
        }

        renderAppErr("-1", "查看账单密码有误");
    }

    @Operation(summary = "清楚校验密码" , description = "清楚校验密码")
    @PostMapping("/clearCheckPwd")
    public void clearCheckPwd() {
        NbDriverEntity driver = getLoginDriver();
        String key = "nb_driver_settle_show_" + driver.getDriverId();
        redisTemplate.delete(key);
        renderAppSuc();
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "修改密码" , description = "修改密码")
    @PostMapping("/changePwd")
    public void changePwd(@RequestParam("oldPwd")String oldPwd, @RequestParam("newPwd")String newPwd) {
        if (StrUtil.isBlank(newPwd) || newPwd.length() < 6) {
            renderAppErr("-1", "新密码长度不能小于6位");
            return;
        }
        NbDriverEntity driver = getLoginDriver();
        if (!driver.getSettlePassword().equals(oldPwd)) {
            renderAppErr("-1", "原密码有误");
            return;
        }
        driver.setSettlePassword(newPwd);
        driverService.updateById(driver);
        renderAppSuc();
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "修改账单密码" , description = "修改账单密码")
    @PostMapping("/changeSettlePwd")
    public void changeSettlePwd(@RequestParam("settlePwd")String settlePwd) {
        if (StrUtil.isBlank(settlePwd) || settlePwd.length() < 4) {
            renderAppErr("-1", "密码不能少于4位");
            return;
        }
        NbDriverEntity driver = getLoginDriver();
        driver.setSettlePassword(settlePwd);
        driverService.updateById(driver);

        renderAppSuc();
    }

    @Operation(summary = "日账单" , description = "日账单")
    @PostMapping("/daySettle")
    public void daySettle(@RequestParam("date")String date) {
        NbDriverEntity driver = getLoginDriver();

        String startTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        List<NbOrderEntity> orders = findOrdersByDriverAndDate(driver.getDriverId(), startTime, endTime);
        stat(orders);   // 统计订单
    }

    private void stat(List<NbOrderEntity> orders) {
        List<NbOrderCostEntity> ocList = new ArrayList<>();
        if (orders.size() > 0) {
            String orderIds = orders.stream().map(NbOrderEntity::getOrderId).map(Object::toString).collect(Collectors.joining(","));
            // "select * from nb_order_cost where order_id in (" + orderIds + ")");
            ocList = orderCostService.list(Wrappers.lambdaQuery(NbOrderCostEntity.class).in(NbOrderCostEntity::getOrderId, orderIds));
        }
        List<BigDecimal> kgRange = new LinkedList<>();
        kgRange.add(new BigDecimal("0"));
        kgRange.add(new BigDecimal("2.5"));
        kgRange.add(new BigDecimal("3"));
        kgRange.add(new BigDecimal("6"));
        kgRange.add(new BigDecimal("9"));
        kgRange.add(new BigDecimal("10"));

        Map<String, List<NbOrderCostEntity>> ocMap = new HashMap<>();
        for (NbOrderCostEntity oc : ocList) {
            BigDecimal weight = Optional.ofNullable(oc.getPkgWeight()).orElse(BigDecimal.ZERO);
            BigDecimal volumeWeight = Optional.ofNullable(oc.getPkgVolumeWeight()).orElse(BigDecimal.ZERO);
            BigDecimal useWeight = weight.max(volumeWeight);
            String name = null;
            for (int i=0; i<kgRange.size(); i++) {
                if (useWeight.compareTo(BigDecimal.ZERO) <= 0) {
                    name = "<=0KG";
                    break;
                }
                if (kgRange.get(i).compareTo(useWeight) >= 0) {
                    name = kgRange.get(i-1) + "-" + kgRange.get(i) + "KG";
                    break;
                }
            }
            if (name == null) {
                name = ">" + kgRange.get(kgRange.size() - 1) + "KG";
            }

            List<NbOrderCostEntity> ocs;
            if (ocMap.containsKey(name)) {
                ocs = ocMap.get(name);
            } else {
                ocs = new ArrayList<>();
            }
            ocs.add(oc);
            ocMap.put(name, ocs);
        }
        Comparator<String> customComparator = new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                if (s1.startsWith("<")) {
                    return -1;
                }
                if (s2.startsWith("<")) {
                    return 1;
                }
                if (s1.startsWith(">")) {
                    return 1;
                }

                return s1.substring(0, 1).compareTo(s2.substring(0, 1));
            }
        };

        Map<String, List<NbOrderCostEntity>> ocSortedMap = new TreeMap<>(customComparator);
        ocSortedMap.putAll(ocMap);

        BigDecimal allSubsidy = new BigDecimal("0");
        BigDecimal allWeightSubsidy = new BigDecimal("0");
        BigDecimal allBase = new BigDecimal("0");
        int allFinishedTotal = 0;

        JSONArray ja = new JSONArray();
        Iterator<Map.Entry<String, List<NbOrderCostEntity>>> iter = ocSortedMap.entrySet().iterator();

        while (iter.hasNext()) {
            Map.Entry<String, List<NbOrderCostEntity>> entry = iter.next();
            String name = entry.getKey();
            List<NbOrderCostEntity> ocs = entry.getValue();

            int pkgFinishedTotal = 0;
            BigDecimal baseTotal = BigDecimal.ZERO;
            BigDecimal weightSubsidyTotal = BigDecimal.ZERO;
            BigDecimal subsidyTotal = BigDecimal.ZERO;
            for (NbOrderCostEntity oc : ocs) {
                if (oc.getCostStatus() == OrderCostDto.COST_STATUS_2_SETTLE) {
                    pkgFinishedTotal ++;
                    // 订单完成，金额才会累计
                    // baseTotal += oc.getDriverBase();
                    // weightSubsidyTotal += oc.getDriverWeightSubsidy();
                    // subsidyTotal += oc.getDriverSubsidy();
                    baseTotal = baseTotal.add(oc.getDriverBase() != null ? oc.getDriverBase() : BigDecimal.ZERO);
                    weightSubsidyTotal = weightSubsidyTotal.add(oc.getDriverWeightSubsidy() != null ? oc.getDriverWeightSubsidy() : BigDecimal.ZERO);
                    subsidyTotal = subsidyTotal.add(oc.getDriverSubsidy() != null ? oc.getDriverSubsidy() : BigDecimal.ZERO);
                } else {
                    log.info("未结算");
                }
            }
            allFinishedTotal += pkgFinishedTotal;

            // baseTotal = new BigDecimal(baseTotal).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            // weightSubsidyTotal = new BigDecimal(weightSubsidyTotal).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            // subsidyTotal = new BigDecimal(subsidyTotal).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            // 保留两位小数
            baseTotal = baseTotal.setScale(2, RoundingMode.HALF_UP);
            weightSubsidyTotal = weightSubsidyTotal.setScale(2, RoundingMode.HALF_UP);
            subsidyTotal = subsidyTotal.setScale(2, RoundingMode.HALF_UP);

            BigDecimal perPkgAmount = BigDecimal.ZERO;
            if (pkgFinishedTotal > 0) {
//                perPkgAmount = new BigDecimal(String.valueOf(baseTotal)).divide(new BigDecimal(String.valueOf(pkgFinishedTotal)), 2, BigDecimal.ROUND_HALF_UP).doubleValue();
                perPkgAmount = baseTotal.divide(new BigDecimal(pkgFinishedTotal), 2, RoundingMode.HALF_UP);
            }
            JSONObject jo = new JSONObject();
            jo.set("name", name);
            jo.set("pkgTotal", ocs.size());
            jo.set("pkgFinishedTotal", pkgFinishedTotal);
            jo.set("baseTotal", baseTotal);
            jo.set("weightSubsidyTotal", weightSubsidyTotal);
            jo.set("subsidyTotal", subsidyTotal);
            jo.set("perPkgAmount", perPkgAmount);

            ja.add(jo);

            // allSubsidy = allSubsidy.add(new BigDecimal(String.valueOf(subsidyTotal)));
            // allWeightSubsidy = allWeightSubsidy.add(new BigDecimal(String.valueOf(weightSubsidyTotal)));
            // allBase = allBase.add(new BigDecimal(String.valueOf(baseTotal)));
            // 累加总补贴等
            allSubsidy = allSubsidy.add(subsidyTotal);
            allWeightSubsidy = allWeightSubsidy.add(weightSubsidyTotal);
            allBase = allBase.add(baseTotal);
        }
        JSONObject ret = new JSONObject();
        ret.set("items", ja);
        ret.set("totalAmount", allSubsidy.add(allWeightSubsidy).add(allBase).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        ret.set("subsidy", allSubsidy.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        ret.set("weightSubsidy", allWeightSubsidy.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        if (ocList.size() == 0) {
            ret.set("finishedRato", "0.00%");
        } else {
            ret.set("finishedRato", new BigDecimal(((allFinishedTotal * 1.0 )/ (ocList.size() * 1.0)) * 100).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "%");
        }

        renderAppData(ret);
    }

    @Operation(summary = "日账单明细" , description = "日账单明细")
    @PostMapping("/dayItems")
    public void dayItems(@RequestParam("date")String date,@RequestParam("type") String type) {
        NbDriverEntity driver = getLoginDriver();
        String startTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";
        List<NbOrderEntity> orders = findOrdersByDriverAndDate(driver.getDriverId(), startTime, endTime);
        // String orderIds = orders.stream().map(Order::getOrderId).map(Object::toString).collect(Collectors.joining(","));

        dealItems(orders, type, driver, date, "daily"); // 处理日账单明细
    }

    // 处理日账单明细
    private void dealItems(List<NbOrderEntity> orders, String type, NbDriverEntity driver, String date, String dir) {
        List<Integer> orderIdList = new ArrayList<>();
        Map<Integer, NbOrderEntity> orderMap = new HashMap<>();
        for (NbOrderEntity order : orders) {
            orderMap.put(order.getOrderId(), order);
            orderIdList.add(order.getOrderId());
        }

        String orderIds = StringUtils.join(orderIdList, ",");

        // "select * from nb_order_cost where order_id in (" + orderIds + ")");
        List<NbOrderCostEntity> ocList = orderCostService.list(new QueryWrapper<NbOrderCostEntity>().in("order_id", orderIds));
        JSONArray ja = new JSONArray();

        BigDecimal weightSubsidy = BigDecimal.ZERO;
        BigDecimal subsidy = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal baseAmount = BigDecimal.ZERO;

        for (NbOrderCostEntity oc : ocList) {
            NbOrderEntity order = orderMap.get(oc.getOrderId());
            BigDecimal useWeight = oc.getPkgWeight().max(oc.getPkgVolumeWeight());

            JSONObject jo = new JSONObject();
            jo.set("lineNo", order.getPickNo());
            jo.set("useWeight", useWeight);
            jo.set("isVolume", oc.getPkgVolumeWeight().compareTo(oc.getPkgWeight()) > 0);
            jo.set("baseAmount", oc.getDriverBase());

            if (oc.getSettleTime() == null) {
                jo.set("settleTime", "-");
            } else {
                jo.set("settleTime", DateFormatUtils.format(oc.getSettleTime(), "yyyy-MM-dd HH:mm"));
            }

            ja.add(jo);

            if (oc.getCostStatus() == OrderCostDto.COST_STATUS_2_SETTLE) {
                // 结算的才进行累计
                weightSubsidy = weightSubsidy.add(oc.getDriverWeightSubsidy());
                subsidy = subsidy.add(oc.getDriverSubsidy());
                baseAmount = baseAmount.add(oc.getDriverBase());
            } else {
                log.info("未结算");
            }
        }
        totalAmount = baseAmount.add(subsidy).add(weightSubsidy);

        // totalAmount = new BigDecimal(totalAmount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        // weightSubsidy = new BigDecimal(weightSubsidy).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        // subsidy = new BigDecimal(subsidy).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        weightSubsidy = weightSubsidy.setScale(2, RoundingMode.HALF_UP);
        subsidy = subsidy.setScale(2, RoundingMode.HALF_UP);
        totalAmount = totalAmount.setScale(2, RoundingMode.HALF_UP);
        baseAmount = baseAmount.setScale(2, RoundingMode.HALF_UP);

        ja.sort(new Comparator<Object>() {
            @Override
            public int compare(Object o1, Object o2) {
                JSONObject jo1 = (JSONObject) o1;
                JSONObject jo2 = (JSONObject) o2;

                String lineNo1 = jo1.getStr("lineNo");
                String lineNo2 = jo2.getStr("lineNo");
                return lineNo1.compareTo(lineNo2);
            }
        });

        if ("download".equals(type)) {
            String downloadFile = "/download/bill/" + driver.getDriverId() + "_" + dir + "_" + date + ".xlsx";
            File file = new File(PathUtil.getWebRootPath() + downloadFile);
            if (file.exists()) {
                file.delete();
            }
            ExcelWriter writer = ExcelUtil.getWriter(PathUtil.getWebRootPath() + downloadFile);
            writer.setColumnWidth(0, 20);
            writer.setColumnWidth(3, 20);
            writer.getOrCreateCell(0, 0).setCellValue("司机号：");
            writer.getOrCreateCell(1, 0).setCellValue(driver.getDriverId());
            writer.getOrCreateCell(2, 0).setCellValue("结算日期：");
            writer.getOrCreateCell(3, 0).setCellValue(date);

            writer.passRows(1);

            List<Map<String, Object>> list = new ArrayList<>();
            for (int i=0; i<ja.size(); i++) {
                JSONObject jo = ja.getJSONObject(i);
                Map<String, Object> row1 = new LinkedHashMap<>();
                row1.put("路区号", jo.getStr("lineNo"));
                row1.put("重量", jo.getBigDecimal("useWeight").setScale(2, RoundingMode.HALF_UP));
                row1.put("基本收入", jo.getBigDecimal("baseAmount").setScale(2, RoundingMode.HALF_UP));
                row1.put("结算时间", jo.getStr("settleTime"));

                list.add(row1);
            }

            ArrayList<Map<String, Object>> rows = CollUtil.newArrayList(list);
            writer.write(rows, true);

            CellStyle style = writer.getCellStyle();
            style.setDataFormat((short)8);

            writer.passCurrentRow();
            writer.getOrCreateCell(0, list.size() + 2).setCellValue("重量补贴收入：");
            writer.merge(list.size() + 2, list.size() + 2, 1, 3, weightSubsidy, false);

            writer.getOrCreateCell(0, list.size() + 3).setCellValue("补贴收入：");
            writer.merge(list.size() + 3, list.size() + 3, 1, 3, subsidy, false);

            writer.getOrCreateCell(0, list.size() + 4).setCellValue("总收入：");
            writer.merge(list.size() + 4, list.size() + 4, 1, 3, totalAmount, false);

            for (int i=0; i<list.size(); i++) {
                writer.setStyle(style, 2, i+2);
            }
            writer.setStyle(style, 1, list.size() + 2);
            writer.setStyle(style, 1, list.size() + 3);
            writer.setStyle(style, 1, list.size() + 4);
            writer.close();

            JSONObject ret = new JSONObject();
            ret.set("path", downloadFile);
            ret.set("filename", driver.getDriverId() + "_" + dir + "_" + date + ".xlsx");

            renderAppData(ret);
            return;
        }
        JSONObject ret = new JSONObject();
        ret.set("items", ja);
        ret.set("totalAmount", totalAmount);
        ret.set("weightSubsidy", weightSubsidy);
        ret.set("subsidy", subsidy);

        renderAppData(ret);
    }

    /**
     * 根据传过来的值
     */
    @Operation(summary = "双周范围" , description = "双周范围")
    @PostMapping("/biweeklyRange")
    public void biweeklyRange(@RequestParam("date")String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date selectedDate = null;
        try {
            selectedDate = sdf.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (selectedDate == null) {
            renderAppErr("-1", "日期选择有误");
            return;
        }

        NbDriverEntity driver = getLoginDriver();
        NbSortingCenterEntity sc = sortingCenterService.getById(driver.getScId());
        Date localDate = NBDUtils.getLocalDate(sc.getScTimezone());

        if (selectedDate.after(localDate)) {
            selectedDate = localDate;
            date = sdf.format(localDate);
        }

        DateTimeFormatter ymdFormater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter mmmFormater = DateTimeFormatter.ofPattern("MMM dd");

        LocalDate currentLd = LocalDate.parse(DateFormatUtils.format(localDate, "yyyy-MM-dd"), ymdFormater);
        LocalDate startLd = LocalDate.of(2024, 2, 12);
        
        while (startLd.isBefore(currentLd)) {
            String from = startLd.format(ymdFormater);
            String displayFrom = startLd.format(mmmFormater);

            LocalDate innerStartLd = startLd;

            startLd = startLd.plusWeeks(2);

            String to = startLd.format(ymdFormater);
            String displayTo = startLd.format(mmmFormater);

            boolean isBreak = false;

            JSONArray ja = new JSONArray();
            while (innerStartLd.isBefore(startLd) || innerStartLd.isEqual(startLd)) {
                String current = innerStartLd.format(ymdFormater);

                if (current.equals(date)) {
                    isBreak = true;
                } else {
                    ja.add(current);
                }

                innerStartLd = innerStartLd.plusDays(1);
            }

            if (isBreak) {
                JSONObject ret = new JSONObject();
                ret.set("dates", ja);
                ret.set("display", displayFrom + "-" + displayTo);
                ret.set("rangeDate", ja.get(0) + "-" + ja.get(ja.size() - 1));

                renderAppData(ret);
                break;
            }

        }
    }

    @Operation(summary = "双周列表" , description = "双周列表")
    @PostMapping("/biweeklyList")
    public void biweeklyList() {
        NbDriverEntity driver = getLoginDriver();
        NbSortingCenterEntity sc = sortingCenterService.getById(driver.getScId());
        Date localDate = NBDUtils.getLocalDate(sc.getScTimezone());

        DateTimeFormatter ymdFormater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter mmmFormater = DateTimeFormatter.ofPattern("MMM dd", Locale.ENGLISH);

        LocalDate currentLd = LocalDate.parse(DateFormatUtils.format(localDate, "yyyy-MM-dd"), ymdFormater);
        LocalDate startLd = LocalDate.of(2024, 2, 12);

        JSONArray ja = new JSONArray();
        while (startLd.isBefore(currentLd)) {
            String from = startLd.format(ymdFormater);
            String displayFrom = startLd.format(mmmFormater);

            startLd = startLd.plusWeeks(2);
            String to = startLd.minusDays(1).format(ymdFormater);
            String displayTo = startLd.minusDays(1).format(mmmFormater);

            JSONObject jo = new JSONObject();
            jo.set("value", from + "," + to);
            jo.set("text", displayFrom + "-" + displayTo);

            ja.add(0, jo);
        }

        renderAppData(ja);
    }

    @Operation(summary = "双周统计" , description = "双周统计")
    @PostMapping("/biweeklyStat")
    public void biweeklyStat(@RequestParam("dateRange")String dateRange) {
        String[] dates = dateRange.split(",");
        String startDate = dates[0];
        String endDate = dates[1];

        NbDriverEntity driver = getLoginDriver();

        String startTime = startDate + " 00:00:00";
        String endTime = endDate + " 23:59:59";

        List<NbOrderEntity> orders = findOrdersByDriverAndDate(driver.getDriverId(), startTime, endTime);

        stat(orders);
    }

    @Operation(summary = "双周明细" , description = "双周明细")
    @PostMapping("/biweeklyItems")
    public void biweeklyItems(@RequestParam("dateRange")String dateRange, @RequestParam("type")String type) {
        NbDriverEntity driver = getLoginDriver();

        String[] dates = dateRange.split(",");
        String startDate = dates[0];
        String endDate = dates[1];

        String startTime = startDate + " 00:00:00";
        String endTime = endDate + " 23:59:59";

        String displayDate = startDate + "-" + endDate;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date sd;
        try {
            sd = sdf.parse(startTime);
            Date ed = sdf.parse(endTime);

            SimpleDateFormat mmmdd = new SimpleDateFormat("MMMdd", Locale.ENGLISH);
            displayDate = mmmdd.format(sd);
            displayDate += ("-" + mmmdd.format(ed));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<NbOrderEntity> orders = findOrdersByDriverAndDate(driver.getDriverId(), startTime, endTime);

        dealItems(orders, type, driver, displayDate, "biweekly");
    }

    private List<NbOrderEntity> findOrdersByDriverAndDate(Integer driverId, String startTime, String endTime) {
        // "select distinct o.* from nb_order o, nb_order_path op "
        //      + "where o.order_id = op.order_id and op.order_status in (205) and o.driver_id = ? and op.add_time >= ? and op.add_time <= ?", driver.getDriverId(), startTime, endTime);
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbOrderEntity.class) // 选择 NbOrderEntity 表的所有字段
                .innerJoin(NbOrderPathEntity.class, NbOrderPathEntity::getOrderId, NbOrderEntity::getOrderId)
                .eq(NbOrderEntity::getDriverId, driverId)
                .in(NbOrderPathEntity::getOrderStatus, 205)
                .ge(NbOrderPathEntity::getAddTime, startTime)
                .le(NbOrderPathEntity::getAddTime, endTime)
                .groupBy(NbOrderEntity::getOrderId); // 使用 groupBy 去重
        return orderService.selectJoinList(NbOrderEntity.class, wrapper);
    }
    
}
