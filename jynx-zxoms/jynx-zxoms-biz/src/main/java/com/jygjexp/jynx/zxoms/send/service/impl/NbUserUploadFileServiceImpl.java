package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbUserUploadFileEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbUserUploadFileMapper;
import com.jygjexp.jynx.zxoms.send.service.NbUserUploadFileService;
import org.springframework.stereotype.Service;
/**
 * 上传的文件
 *
 * <AUTHOR>
 * @date 2024-11-12 09:36:08
 */
@Service
public class NbUserUploadFileServiceImpl extends ServiceImpl<NbUserUploadFileMapper, NbUserUploadFileEntity> implements NbUserUploadFileService {
}