package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbDriverService;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;
import com.jygjexp.jynx.zxoms.vo.DriverVo;
import com.jygjexp.jynx.zxoms.vo.NbDriverExcelVo;
import com.jygjexp.jynx.zxoms.vo.PaisongDriverPageVo;
import com.route4me.sdk.exception.APIException;
import com.route4me.sdk.services.users.User;
import com.route4me.sdk.services.v5.V5Array;
import com.route4me.sdk.services.v5.VehicleCapacity;
import com.route4me.sdk.services.vehicles.Vehicles;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 司机
 *
 * <AUTHOR>
 * @date 2024-10-15 22:42:15
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbDriver" )
@Tag(description = "nbDriver" , name = "司机管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbDriverController {

    private final  NbDriverService nbDriverService;
    private final Route4MeUtil route4MeUtil;

    /**
     * 分页查询
     * @param page
     * @param entity
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriver_view')" )
    public R getNbDriverPage(@ParameterObject Page page, @ParameterObject NbDriverEntity entity) {
        //  查询前校验逻辑
        nbDriverService.checkDriverScId();
        return R.ok(nbDriverService.search(page, entity));
    }

    // 派送司机分页列表
    @Operation(summary = "派送司机分页列表" , description = "派送司机分页列表" )
    @GetMapping("/pagePaisongDriver" )
    @PreAuthorize("@pms.hasPermission('driver_paisong_view')" )
    public R pagePaisongDriver(@ParameterObject Page page, @ParameterObject PaisongDriverPageVo vo){
        return R.ok(nbDriverService.getPaisongDriver(page, vo));
    }

    /**
     * 通过id查询司机
     * @param driverId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{driverId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriver_view')" )
    public R getById(@PathVariable("driverId" ) Integer driverId) {
        return R.ok(nbDriverService.getById(driverId));
    }

    /**
     * 查询所属司机Leader
     * @return
     */
    @Operation(summary = "查询所属司机Leader" , description = "查询所属司机Leader" )
    @GetMapping("getPDriverId")
    public R getPDriverId(@ParameterObject Page page, @ParameterObject NbDriverEntity entity){
        return R.ok(nbDriverService.pageListPDriverId(page, entity));
    }

    /**
     * 查询审核中的司机
     * @param driverId id
     * @return R
     */
    @Operation(summary = "司机审核" , description = "司机审核" )
    @GetMapping("/findAuditDriver/{driverId}" )
//    @PreAuthorize("@pms.hasPermission('driver_view')" )
    public R<List<NbDriverEntity>> findAuditDriver(@PathVariable("driverId" ) Integer driverId) {
        return R.ok(nbDriverService.findAuditDriver(driverId));
    }

    /**
     * 新增司机
     * @param nbDriver 司机
     * @return R
     */
    @Operation(summary = "新增司机" , description = "新增司机" )
    @SysLog("新增司机" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriver_add')" )
    public R save(@RequestBody NbDriverEntity nbDriver) {
        //  添加司机前校验信息
        nbDriverService.checkAddDriverBefore(nbDriver);
        nbDriver.setBusinessType(nbDriver.getBusinessType());
        nbDriver.setRegTime(new Date());    // 设置注册时间

        // 校验新增或编辑司机手机号是否重复
        Boolean isExist = nbDriverService.checkMobile(nbDriver.getMobile());
        if (isExist){
            return LocalizedR.failed("nbdriver.mobile.unique.cannot.be.duplicated", nbDriver.getMobile());
        }
        boolean save = nbDriverService.save(nbDriver);
        if (save){
            //司机新增信息成功之后，改变状态为待审核
            nbDriver.setAuditStatus(2);
            nbDriverService.updateById(nbDriver);
            return LocalizedR.ok("nbdriver.create.successfully", Optional.ofNullable(null));
//             创建成功后处理逻辑
//            R r = nbDriverService.processAddSucceed(nbDriver);
//            if (r.isOk()){
//                return R.ok("司机创建成功");
//            }else {
//                return R.failed("司机创建失败：" + r.getMsg());
//            }
        }
        return LocalizedR.failed("nbdriver.create.failed", Optional.ofNullable(null));
    }



    /**
     * app完善信息新增司机
     * @param nbDriver 司机
     * @return R
     */
    @Operation(summary = "app完善信息新增司机" , description = "app完善信息新增司机" )
    @SysLog("app完善信息新增司机" )
    @PostMapping("app/driver/save")
    public R DriverSave(@RequestBody NbDriverEntity nbDriver) {
        //  添加司机前校验信息
        nbDriverService.checkAddDriverBefore(nbDriver);
        nbDriver.setBusinessType(nbDriver.getBusinessType());
        nbDriver.setRegTime(new Date());    // 设置注册时间

        // 校验新增或编辑司机手机号是否重复
        Boolean isExist = nbDriverService.checkMobile(nbDriver.getMobile());
        if (isExist){
            return LocalizedR.failed("nbdriver.mobile.unique.cannot.be.duplicated", nbDriver.getMobile());
        }
        boolean save = nbDriverService.save(nbDriver);
        if (save){
            //司机新增信息成功之后，改变状态为待审核
            nbDriver.setAuditStatus(2);
            nbDriverService.updateById(nbDriver);
            return LocalizedR.ok("nbdriver.create.successfully", Optional.ofNullable(null));
        }
        return LocalizedR.failed("nbdriver.create.failed", Optional.ofNullable(null));
    }


    /**
     * app修改司机
     * @param nbDriver 司机
     * @return R
     */
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    @Operation(summary = "app修改司机" , description = "app修改司机" )
    @SysLog("app修改司机" )
    @PostMapping("app/driver/update")
    public R driverUpdateById(@RequestBody NbDriverEntity nbDriver, HttpServletRequest request) {
        //  修改前设置属性
        Integer driverId = nbDriver.getDriverId();
        NbDriverEntity driver = nbDriverService.getById(driverId);
        request.setAttribute("__DRIVER_UPDATE_BEFORE", driver);

        // 校验新增或编辑司机手机号是否重复
        Boolean isExist = nbDriverService.checkUpdateMobile(nbDriver.getMobile(), driverId);
        if (isExist){
            return LocalizedR.failed("nbdriver.mobile.unique.cannot.be.duplicated", nbDriver.getMobile());
        }

        //退件司机更改业务类型为派送的情况
        if (driver.getBusinessType() == 2 && (nbDriver.getBusinessType() == 1 || nbDriver.getBusinessType() == 3)){

            //创建Route4Me用户
            R r = route4MeUtil.createUser(nbDriver);
            if (r.isOk()) {
                User r4mUser = (User) r.getData();
                nbDriver.setRoute4meMemberId(Integer.valueOf(r4mUser.getMemberId()));
                //nbDriverService.updateById(nbDriver);
            } else {
                return LocalizedR.failed("nbdriver.created.failed", r.getMsg());
            }

            //创建Route4Me车辆
            Vehicles vehicle = route4MeUtil.createVehicle(nbDriver);
            if (StrUtil.isNotBlank(vehicle.getVehicleId())) {
                nbDriver.setRoute4meVehicleId(vehicle.getVehicleId());
                //nbDriverService.updateById(nbDriver);
            } else {
                return LocalizedR.failed("nbdriver.vehicle.created.failed", nbDriver.getRoute4meMemberId());
            }

        }

        Boolean b = nbDriverService.updateById(nbDriver);
        if (b) {
            Boolean b1 = driver.getBusinessType() == 1 || driver.getBusinessType() == 3;
            if(b1 && driver.getAuditStatus() == 3){
                // 判断 firstName 或 lastName 是否变化，Objects.equals可预防空指针异常
                boolean isNameChanged = !Objects.equals(driver.getFirstName(), nbDriver.getFirstName()) ||
                        !Objects.equals(driver.getLastName(), nbDriver.getLastName());

                if (isNameChanged) {
                    // 司机信息修改成功，同步修改 Route4Me 的司机和车辆信息
                    route4MeUtil.updateUser(nbDriver);
                    route4MeUtil.updateVehicle(nbDriver);
                }
            }
            NbDriverEntity beforeDriver = (NbDriverEntity) request.getAttribute("__DRIVER_UPDATE_BEFORE");
            if (beforeDriver.getRoute4meMemberId() != null && beforeDriver.getRoute4meMemberId() > 0) {
                if (beforeDriver.getIsValid() && nbDriver.getIsValid() == false) {
                    // 被置为无效，删除route4me
                    route4MeUtil.removeUser(beforeDriver);
                    nbDriver.setRoute4meMemberId(0);
                    nbDriverService.updateById(nbDriver);
                }
            } else {
                // 被置为有效
                if (beforeDriver.getIsValid() == false && nbDriver.getIsValid() && (beforeDriver.getBusinessType() ==1 || beforeDriver.getBusinessType() == 3)) {
                    // 恢复用户
                    R r = route4MeUtil.createUser(beforeDriver);
                    if (r.isOk()) {
                        User r4mUser = (User) r.getData();
                        nbDriver.setRoute4meMemberId(Integer.valueOf(r4mUser.getMemberId()));
                        nbDriverService.updateById(nbDriver);
                    }
                }
            }

        }
        return R.ok(b);
    }





    @Operation(summary = "查询R4m车辆容量profile" , description = "查询R4m车辆容量profile")
    @SysLog("查询R4m车辆容量profile" )
    @PostMapping("/listR4mVehicleCapacityProfile")
    public R listR4mVehicleCapacityProfile() {
        JSONArray vcJa = new JSONArray();
        V5Array<VehicleCapacity> vcs = route4MeUtil.listVehicleCapacity();
        if (vcs != null) {
            List<VehicleCapacity> vcList = vcs.getData();
            for (VehicleCapacity vc : vcList) {
                String name = vc.getName();
                Integer id = vc.getVehicleCapacityProfileId();
//                    nbDriver.setR4mVehicleCapacityProfileId(id.toString());
                JSONObject vcObject = new JSONObject();
                vcObject.set("id", id);
                vcObject.set("cn", name);
                vcJa.add(vcObject);
            }
        }
        return R.ok(vcJa);
    }

    /**
     * 修改司机
     * @param nbDriver 司机
     * @return R
     */
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    @Operation(summary = "修改司机" , description = "修改司机" )
    @SysLog("修改司机" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriver_edit')" )
    public R updateById(@RequestBody NbDriverEntity nbDriver, HttpServletRequest request) {
        //  修改前设置属性
        Integer driverId = nbDriver.getDriverId();
        NbDriverEntity driver = nbDriverService.getById(driverId);
        request.setAttribute("__DRIVER_UPDATE_BEFORE", driver);

        // 校验新增或编辑司机手机号是否重复
        Boolean isExist = nbDriverService.checkUpdateMobile(nbDriver.getMobile(), driverId);
        if (isExist){
            return LocalizedR.failed("nbdriver.mobile.unique.cannot.be.duplicated", nbDriver.getMobile());
        }

        //退件司机更改业务类型为派送的情况
        if (driver.getBusinessType() == 2 && (nbDriver.getBusinessType() == 1 || nbDriver.getBusinessType() == 3)){

            //创建Route4Me用户
            R r = route4MeUtil.createUser(nbDriver);
            if (r.isOk()) {
                User r4mUser = (User) r.getData();
                nbDriver.setRoute4meMemberId(Integer.valueOf(r4mUser.getMemberId()));
                //nbDriverService.updateById(nbDriver);
            } else {
                return LocalizedR.failed("nbdriver.created.failed", r.getMsg());
//                String rawErrorMsg = r.getMsg();
//
//                // 提取 errors 内容
//                StringBuilder parsedErrors = new StringBuilder();
//                if (rawErrorMsg != null && rawErrorMsg.contains("{")) {
//                    try {
//                        String jsonPart = rawErrorMsg.substring(rawErrorMsg.indexOf("{"));
//                        cn.hutool.json.JSONObject jsonObject = cn.hutool.json.JSONUtil.parseObj(jsonPart);
//
//                        if (jsonObject.containsKey("errors")) {
//                            Object errorsObj = jsonObject.get("errors");
//
//                            if (errorsObj instanceof JSONArray) {
//                                JSONArray errorsArray = (JSONArray) errorsObj;
//                                for (int i = 0; i < errorsArray.size(); i++) {
//                                    parsedErrors.append(errorsArray.getStr(i));
//                                    if (i < errorsArray.size() - 1) {
//                                        parsedErrors.append("; ");
//                                    }
//                                }
//                            } else if (errorsObj instanceof JSONObject) {
//                                JSONObject errorsDict = (JSONObject) errorsObj;
//                                for (String key : errorsDict.keySet()) {
//                                    parsedErrors.append(key);
//                                    break; // 只取第一个
//                                }
//                            } else if (errorsObj instanceof String) {
//                                parsedErrors.append((String) errorsObj);
//                            }
//
//                        }
//                    } catch (Exception ex) {
//                        log.warn("JSON parsing failed, using raw error message", ex);
//                    }
//                }
//
//                String finalErrorMsg = parsedErrors.length() > 0 ? parsedErrors.toString() : rawErrorMsg;
//                return LocalizedR.failed("nbdriver.created.failed", finalErrorMsg);
            }

            //创建Route4Me车辆
            Vehicles vehicle = route4MeUtil.createVehicle(nbDriver);
            if (StrUtil.isNotBlank(vehicle.getVehicleId())) {
                nbDriver.setRoute4meVehicleId(vehicle.getVehicleId());
                //nbDriverService.updateById(nbDriver);
            } else {
                return LocalizedR.failed("nbdriver.vehicle.created.failed", nbDriver.getRoute4meMemberId());
            }

        }

        Boolean b = nbDriverService.updateById(nbDriver);
         if (b) {
            Boolean b1 = driver.getBusinessType() == 1 || driver.getBusinessType() == 3;
            if(b1 && driver.getAuditStatus() == 3){
                // 判断 firstName 或 lastName 是否变化，Objects.equals可预防空指针异常
                boolean isNameChanged = !Objects.equals(driver.getFirstName(), nbDriver.getFirstName()) ||
                        !Objects.equals(driver.getLastName(), nbDriver.getLastName());

                if (isNameChanged) {
                    // 司机信息修改成功，同步修改 Route4Me 的司机和车辆信息
                    route4MeUtil.updateUser(nbDriver);
                    route4MeUtil.updateVehicle(nbDriver);
                }
            }
            NbDriverEntity beforeDriver = (NbDriverEntity) request.getAttribute("__DRIVER_UPDATE_BEFORE");
            if (beforeDriver.getRoute4meMemberId() != null && beforeDriver.getRoute4meMemberId() > 0) {
                if (beforeDriver.getIsValid() && nbDriver.getIsValid() == false) {
                    // 被置为无效，删除route4me
                    route4MeUtil.removeUser(beforeDriver);
                    nbDriver.setRoute4meMemberId(0);
                    nbDriverService.updateById(nbDriver);
                }
            } else {
                // 被置为有效
                if (beforeDriver.getIsValid() == false && nbDriver.getIsValid() && (beforeDriver.getBusinessType() ==1 || beforeDriver.getBusinessType() == 3)) {
                    // 恢复用户
                    R r = route4MeUtil.createUser(beforeDriver);
                    if (r.isOk()) {
                        User r4mUser = (User) r.getData();
                        nbDriver.setRoute4meMemberId(Integer.valueOf(r4mUser.getMemberId()));
                        nbDriverService.updateById(nbDriver);
                    }
                }
            }

        }
        return R.ok(b);
    }

    /**
     * 通过id删除司机
     * @param ids driverId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机" , description = "通过id删除司机" )
    @SysLog("通过id删除司机" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriver_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        NbDriverEntity driver = nbDriverService.getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getDriverId, ids[0]));
        boolean b = nbDriverService.removeBatchByIds(CollUtil.toList(ids));
        //删除操作成功执行之后，同步执行删除r4m用户
        if (b && (driver.getBusinessType() == 1 || driver.getBusinessType() == 3)){
            route4MeUtil.removeUser(driver);
            driver.setRoute4meMemberId(0);
        }
        return R.ok(b);
    }


    /**
     * 导出excel 表格
     * @param nbDriver 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('driver_export')" )
    public List<NbDriverExcelVo> export(NbDriverEntity nbDriver, Integer[] ids) {
        return nbDriverService.getExcel(nbDriver, ids);
    }

    @Operation(summary = "审核通过" , description = "审核通过" )
    @SysLog("审核通过" )
    @PostMapping("/auditPass")
    @PreAuthorize("@pms.hasPermission('driver_auditPass')" )
    public R auditPass(@RequestBody NbDriverEntity drivers){
        return nbDriverService.auditPass(drivers);
    }
    @Operation(summary = "审核拒绝" , description = "审核拒绝" )
    @SysLog("审核拒绝" )
    @PostMapping("/auditRefuse/{driverId}")
//    @PreAuthorize("@pms.hasPermission('driver_auditRefuse')" )
    public R auditRefuse(Integer driverId){
        return nbDriverService.auditRefuse(driverId);
    }

    @Operation(summary = "审核驳回" , description = "审核驳回" )
    @SysLog("审核驳回" )
    @PostMapping("/auditRefuseReason")
    @PreAuthorize("@pms.hasPermission('driver_auditRefuseReason')" )
    public R auditRefuseReason(@RequestBody DriverVo driverVo){
        return nbDriverService.auditRefuseReason(driverVo);
    }

    @Operation(summary = "司机置无效" , description = "司机置无效" )
    @SysLog("司机置无效" )
    @PostMapping("/driverInvalid")
    @PreAuthorize("@pms.hasPermission('zxoms_driver_driverInvalid')" )
    public R driverInvalid(Integer driverId){
        return nbDriverService.driverInvalid(driverId);
    }

    @Operation(summary = "司机置有效" , description = "司机置有效" )
    @SysLog("司机置有效" )
    @PostMapping("/driverValid")
    @PreAuthorize("@pms.hasPermission('zxoms_driver_driverValid')" )
    public R driverValid(Integer driverId){
        return nbDriverService.driverValid(driverId);
    }

    @Operation(summary = "设置邮箱" , description = "设置邮箱" )
    @SysLog("设置邮箱" )
    @PostMapping("/modifyEmail")
//    @PreAuthorize("@pms.hasPermission('driver_modifyEmail')" )
    public R modifyEmail(@RequestBody DriverVo driverVo){
        return nbDriverService.modifyEmail(driverVo.getDriverId(), driverVo.getEmail());
    }

    @Operation(summary = "更新车辆信息" , description = "更新车辆信息" )
    @SysLog("更新车辆信息" )
    @PostMapping("/updateVehicle")
//    @PreAuthorize("@pms.hasPermission('driver_updateVehicle')" )
    public R updateVehicle(@RequestParam Integer driverId){

        return nbDriverService.updateVehicle(driverId);
    }

    @Operation(summary = "app司机修改密码", description = "app司机修改密码")
    @SysLog("app司机修改密码")
    @PostMapping("/appChangePassword")
    public R appChangePassword(@RequestParam("mobile") String mobile,@RequestParam("password") String password) {
        return nbDriverService.UpdatePassword(mobile,password);
    }

    @Operation(summary = "查询全部退件司机名称" , description = "查询全部退件司机名称" )
    @GetMapping("/returnDriverNameList")
    public R returnDriverNameList() {
;        LambdaQueryWrapper<NbDriverEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(NbDriverEntity::getDriverId, NbDriverEntity::getDriverName, NbDriverEntity::getIsValid).groupBy(NbDriverEntity::getDriverId)
                .in(NbDriverEntity::getBusinessType, 2,3).eq(NbDriverEntity::getAuditStatus,3);
        return R.ok(nbDriverService.list(wrapper));
    }

    @Operation(summary = "查询全部派送司机名称" , description = "查询全部派送司机名称" )
    @GetMapping("/driverNameList")
    public R driverNameList() {
        LambdaQueryWrapper<NbDriverEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(NbDriverEntity::getDriverId, NbDriverEntity::getDriverName, NbDriverEntity::getIsValid).groupBy(NbDriverEntity::getDriverId)
                .in(NbDriverEntity::getBusinessType, 1,3).eq(NbDriverEntity::getAuditStatus,3);
        return R.ok(nbDriverService.list(wrapper));
    }

    /**
     * 上传文件 文件名采用uuid,避免原始文件名中带"-"符号导致下载的时候解析出现异常
     * @param file 资源
     * @param dir 文件夹
     * @return R(/ admin / bucketName / filename)
     */
    @Operation(summary = "图片上传", description = "图片上传")
    @SysLog("图片上传")
    @PostMapping(value = "/upload")
    public R upload(@RequestPart("file") MultipartFile file, @RequestParam(value = "dir", required = false) String dir,
                    @RequestParam(value = "groupId", required = false) Long groupId,
                    @RequestParam(value = "type", required = false) String type) {
        return nbDriverService.uploadFile(file, dir, groupId, type);
    }

}