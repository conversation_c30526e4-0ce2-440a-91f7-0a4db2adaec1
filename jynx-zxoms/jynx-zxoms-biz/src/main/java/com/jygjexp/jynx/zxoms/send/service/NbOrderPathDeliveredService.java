package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbOrderPathEntity;
import com.jygjexp.jynx.zxoms.vo.NbOrderPathDeliveredPageVo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderPathExcelVo;

import java.util.List;

/**
 * @Author: chenchang
 * @Description:订单轨迹
 * @Date: 2024/10/18 19:56
 */
public interface NbOrderPathDeliveredService extends IService<NbOrderPathEntity> {

    // 签收记录分页查询
    Page<NbOrderPathDeliveredPageVo> search(Page page, NbOrderPathDeliveredPageVo vo);

    List<NbOrderPathExcelVo> getExcel(NbOrderPathDeliveredPageVo vo, Integer[] ids);    // 签收记录导出Excel
}
