package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.CitiesEntity;
import com.jygjexp.jynx.zxoms.entity.CountriesEntity;
import com.jygjexp.jynx.zxoms.entity.NbMerchantEntity;
import com.jygjexp.jynx.zxoms.entity.StatesEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbMerchantMapper;
import com.jygjexp.jynx.zxoms.send.service.NbMerchantService;
import com.jygjexp.jynx.zxoms.vo.MerchantPageVo;
import com.jygjexp.jynx.zxoms.send.vo.NbMerchantExcelVo;
import com.jygjexp.jynx.zxoms.send.vo.NbMerchantQuoteExcelVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家
 *
 * <AUTHOR>
 * @date 2024-09-30 22:34:37
 */
@Service
@RequiredArgsConstructor
public class NbMerchantServiceImpl extends ServiceImpl<NbMerchantMapper, NbMerchantEntity> implements NbMerchantService {
    private final NbMerchantMapper nbMerchantMapper;

    @Override
    public Page search(Page page, MerchantPageVo vo) {
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return nbMerchantMapper.selectJoinPage(page, NbMerchantEntity.class, wrapper);
    }

    // 客户导出Excel
    @Override
    public List<NbMerchantExcelVo> getExcel(MerchantPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        return nbMerchantMapper.selectJoinList(NbMerchantExcelVo.class, wrapper);
    }
    private MPJLambdaWrapper getWrapper(MerchantPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbMerchantEntity> wrapper = new MPJLambdaWrapper<NbMerchantEntity>();
        wrapper.eq(ObjectUtil.isNotNull(vo.getMerchantId()), NbMerchantEntity::getMerchantId, vo.getMerchantId())   // 条件查询 客户ID
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), NbMerchantEntity::getIsValid, vo.getIsValid())            // 条件查询 启用状态
                .like(StrUtil.isNotBlank(vo.getName()), NbMerchantEntity::getName, vo.getName());    // 条件查询 客户名称
        String addTime = vo.getAddTime();
        if (addTime != null) {
            int splitIndex = addTime.indexOf(":", addTime.indexOf(":") + 1) + 3;
            String startTime = addTime.substring(0, splitIndex);
            String endTime = addTime.substring(splitIndex + 1);
            wrapper.ge(NbMerchantEntity::getCreateTime, startTime).le(NbMerchantEntity::getCreateTime, endTime);    // 条件查询 创建时间
        }
        wrapper.selectAll(NbMerchantEntity.class)
                .selectAs(CountriesEntity::getName, MerchantPageVo.Fields.countriesName)
                .selectAs(StatesEntity::getName, MerchantPageVo.Fields.statesName)
                .selectAs(CitiesEntity::getName, MerchantPageVo.Fields.cityName)
                .leftJoin(CountriesEntity.class, CountriesEntity::getId, NbMerchantEntity::getWhCountryId)
                .leftJoin(StatesEntity.class, StatesEntity::getId, NbMerchantEntity::getWhProvinceId)
                .leftJoin(CitiesEntity.class, CitiesEntity::getId, NbMerchantEntity::getWhCityId)
                .orderByDesc(NbMerchantEntity::getCreateTime)
                .eq(ObjectUtil.isNotNull(vo.getWhCountryId()), NbMerchantEntity::getWhCountryId, 39)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbMerchantEntity::getMerchantId, ids);

        return wrapper;
    }

    // 客户报价分页查询
    @Override
    public Page searchQuote(Page page, MerchantPageVo vo) {
        MPJLambdaWrapper quoteWrapper = getQuoteWrapper(vo, null);
        return nbMerchantMapper.selectJoinPage(page, MerchantPageVo.class, quoteWrapper);
    }

    // 客户报价导出Excel
    @Override
    public List<NbMerchantQuoteExcelVo> getQuoteExcel(MerchantPageVo vo, Integer[] ids) {
        MPJLambdaWrapper quoteWrapper = getQuoteWrapper(vo, ids);
        return nbMerchantMapper.selectJoinList(NbMerchantQuoteExcelVo.class, quoteWrapper);
    }

    private MPJLambdaWrapper getQuoteWrapper(MerchantPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbMerchantEntity> wrapper = new MPJLambdaWrapper<NbMerchantEntity>();
        wrapper.eq(ObjectUtil.isNotNull(vo.getMerchantId()), NbMerchantEntity::getMerchantId, vo.getMerchantId())   // 条件查询 客户ID
                .like(StrUtil.isNotBlank(vo.getName()), NbMerchantEntity::getMerchantCode, vo.getName())           // 条件查询 客户名称
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbMerchantEntity::getMerchantId, ids);
        String updateTime = vo.getQueryUpdateTime();
        if (updateTime != null) {
            int splitIndex = updateTime.indexOf(":", updateTime.indexOf(":") + 1) + 3;
            String startTime = updateTime.substring(0, splitIndex);
            String endTime = updateTime.substring(splitIndex + 1);
            wrapper.select(NbMerchantEntity::getMerchantId, NbMerchantEntity::getName, NbMerchantEntity::getMerchantCode,
                            NbMerchantEntity::getChargeModel, NbMerchantEntity::getDiscount, NbMerchantEntity::getMultiMinWeight, NbMerchantEntity::getUpdateTime)
                    .ge(NbMerchantEntity::getUpdateTime, startTime).le(NbMerchantEntity::getUpdateTime, endTime);     // 条件查询 更新时间
        }
        wrapper.orderByDesc(NbMerchantEntity::getMerchantId);
        return wrapper;
    }

    // 校验唯一编码在客户列表中是否已存在，如果存在则提示不能重复添加
    @Override
    public Boolean checkMerchantCode(NbMerchantEntity basicNbMerchant) {
        Integer merchantId = basicNbMerchant.getMerchantId();
        String merchantCode = basicNbMerchant.getMerchantCode();
        if (StrUtil.isBlank(merchantCode)) {
            return false;
        }
        int count = Math.toIntExact(nbMerchantMapper.selectCount(new LambdaQueryWrapper<NbMerchantEntity>()
                .eq(NbMerchantEntity::getMerchantCode, merchantCode).ne(ObjectUtil.isNotNull(merchantId), NbMerchantEntity::getMerchantId, merchantId))); // 排除自身
        // 如果 count > 0，则存在重复的 merchantCode
        return count > 0;
    }

}