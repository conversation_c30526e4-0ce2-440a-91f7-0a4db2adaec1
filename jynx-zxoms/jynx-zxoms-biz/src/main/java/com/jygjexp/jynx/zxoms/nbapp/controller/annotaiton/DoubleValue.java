package com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton;

import com.jygjexp.jynx.zxoms.nbapp.validator.DoubleValueValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/7 17:36
 */
@Documented
@Constraint(validatedBy = DoubleValueValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DoubleValue {
    String message() default "Must be a valid double value";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
