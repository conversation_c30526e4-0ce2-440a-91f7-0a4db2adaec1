package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbOrderMpsEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderMpsMapper;
import com.jygjexp.jynx.zxoms.send.service.NbOrderMpsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 订单跟踪
 *
 * <AUTHOR>
 * @date 2024-10-17 21:38:26
 */
@Service
@RequiredArgsConstructor
public class NbOrderMpsServiceImpl extends ServiceImpl<NbOrderMpsMapper, NbOrderMpsEntity> implements NbOrderMpsService {
    private final NbOrderMpsMapper nbOrderMpsMapper;

    @Override
    public NbOrderMpsEntity findOrderMpsByOrderId(Integer orderId) {
        return getOne(new LambdaQueryWrapper<NbOrderMpsEntity>().eq(NbOrderMpsEntity::getOrderId, orderId));
    }

}