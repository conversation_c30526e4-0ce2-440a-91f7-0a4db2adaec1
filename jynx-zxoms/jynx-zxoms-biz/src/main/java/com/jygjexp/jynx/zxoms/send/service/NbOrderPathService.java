package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbOrderPathEntity;
import com.jygjexp.jynx.zxoms.entity.NbPatchPathOrderEntity;
import com.jygjexp.jynx.zxoms.vo.NbOrderPathPageVo;
import com.jygjexp.jynx.zxoms.vo.OrderOs290PageVo;

import java.util.List;

public interface NbOrderPathService extends IService<NbOrderPathEntity> {

    void updateByOrderId(Integer orderId);

    List<NbOrderPathEntity> findByOrderIdAsc(Integer orderId);

    NbOrderPathEntity findOneOrderPathByOrderIdAndOrderStatus(Integer orderId, int orderStatus200ParcelScanned);

    // 签收记录分页查询
    Page<NbOrderPathPageVo> search(Page page, NbOrderPathPageVo vo);

    Page<OrderOs290PageVo> pageOrderOs290(Page page, OrderOs290PageVo vo);  // 配送失败订单分页查询

    List<NbOrderPathEntity> listOrderPathByOrderId(Integer orderId);    //根据订单ID查询订单轨迹

}