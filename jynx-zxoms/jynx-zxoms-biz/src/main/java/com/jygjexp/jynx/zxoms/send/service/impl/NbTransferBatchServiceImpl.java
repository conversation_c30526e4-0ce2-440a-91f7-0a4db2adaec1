package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Sets;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.common.websocket.config.WebSocketMessageSender;
import com.jygjexp.jynx.zxoms.dto.*;
import com.jygjexp.jynx.zxoms.entity.*;

import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.dto.R4mTobeUpdateRouter;
import com.jygjexp.jynx.zxoms.send.mapper.*;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.utils.ConfigUtil;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;
import com.jygjexp.jynx.zxoms.send.vo.NbTransferBatchExcelVo;

import com.jygjexp.jynx.zxoms.vo.NbTransferBatchPageVo;
import com.jygjexp.jynx.zxoms.vo.TransferBatchCostPageVo;
import com.route4me.sdk.services.routing.Address;
import com.route4me.sdk.services.routing.Parameters;
import com.route4me.sdk.services.routing.Route;
import com.route4me.sdk.services.routing.RouteDeletedResponse;
import com.route4me.sdk.services.vehicles.Vehicles;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 转运批次（路区）
 *
 * <AUTHOR>
 * @date 2024-10-12 18:42:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NbTransferBatchServiceImpl extends ServiceImpl<NbTransferBatchMapper, NbTransferBatchEntity> implements NbTransferBatchService {
    private static boolean isSyncRoute = false;

    private final NbOrderCostService nbOrderCostService;
    private final NbOrderService nbOrderService;
    private final NbOrderPathService nbOrderPathService;
    private final RemoteUserService remoteUserService;
    private final NbTransferBatchCostModifyLogService modifyLogService;

    private final NbTransferBatchMapper nbTransferBatchMapper;
    private final NbTransferBatchOrderMapper nbTransferBatchOrderMapper;
    private final NbOrderMapper nbOrderMapper;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbDriverMapper nbDriverMapper;
    private final NbTransferCenterMapper nbTransferCenterMapper;
    private final NbR4mOptimizationLogMapper nbR4mOptimizationLogMapper;
    private final NbR4mRouteCreateLogMapper nbR4mRouteCreateLogMapper;
    private final NbOrderBatchMapper nbOrderBatchMapper;
    private final CommonDataUtil commonDataUtil;
    private final Route4MeUtil route4MeUtil;
    private final ConfigUtil configUtil;

    /**
     * 分页查询路区列表
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<NbTransferBatchPageVo> search(Page page, NbTransferBatchPageVo vo) {
        // select driver_id, first_name firstName，last_name lastName, email, mobile from nb_driver; ds=nbd;
        // select tc_id id, center_name 名称 from nb_transfer_center; ds=nbd;
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return nbTransferBatchMapper.selectJoinPage(page, NbTransferBatchPageVo.class, wrapper);
    }

    /**
     * 修改路区价格-添加补贴
     * @param batchIds
     * @param amount
     * @param note
     * @return
     */
    @Override
    public R addSubsidy(String batchIds, BigDecimal amount, String note) {
//        String batchIdsStr = getSelectValue("batch_id");
        String[] batchIdArr = batchIds.split(",");
        Map<Integer, List<NbOrderEntity>> orderMap = new HashMap<>();

        for (String batchIdStr : batchIdArr) {
            int batchId = Integer.valueOf(batchIdStr);
            NbTransferBatchEntity tb = getById(batchId);
            if (tb == null) {
                return LocalizedR.failed("nbTransferBatch.the.road.area.does.not.exist", batchId);
            }

            if ((System.currentTimeMillis() - tb.getAddTime().getTime()) / 1000 / 60 / 60 / 24 > 2.2) {

                R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
                SysUser user = sysUserR.getData();
//                Boolean driver_subsidy_day_limit = user.getBoolean("driver_subsidy_day_limit");

                // 2024-02-29 卟卟卟：天数限制可配
//                if (driver_subsidy_day_limit == null || driver_subsidy_day_limit == false) {
//                    return R.failed("-1", "超过两天的路区[" + tb.getBatchNo() + "]不能修改，创建时间：" + DateFormatUtils.format(tb.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
//                }

            }

            //     select * from nb_transfer_batch_order tbo, nb_order o where tbo.order_id = o.order_id and tbo.batch_id = ?
            MPJLambdaWrapper<NbTransferBatchOrderEntity> wrapper = new MPJLambdaWrapper<>();
            wrapper.selectAll(NbOrderEntity.class)
                    .eq(NbTransferBatchOrderEntity::getBatchId, batchId)
                    .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbTransferBatchOrderEntity::getOrderId);
            List<NbOrderEntity> orders = nbTransferBatchOrderMapper.selectJoinList(NbOrderEntity.class, wrapper);
            if (orders.size() == 0) {
                return LocalizedR.failed("nbTransferBatch.there.are.no.orders.in.this.area", tb.getBatchNo());
            }

            List<NbOrderEntity> partAllotOrders = new ArrayList<>(); // 参与分配的订单
            for (NbOrderEntity order : orders) {
                NbOrderCostEntity oc = nbOrderCostService.getById(order.getOrderId());
                if (oc == null || Objects.equals(oc.getDriverBase(), BigDecimal.ZERO)) {
                    //  select * from nb_order_path where order_id = ? and order_status = ? limit 1", order.getOrderId(), Order.ORDER_STATUS_200_PARCEL_SCANNED
                    NbOrderPathEntity op200 = nbOrderPathService.findOneOrderPathByOrderIdAndOrderStatus(order.getOrderId(), OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
                    if (op200 != null) {
                        return LocalizedR.failed("nbTransferBatch.The.allocation.of.benchmark.fees.has.not.been.completed.yet", order.getOrderId());
                    }
                } else {
                    partAllotOrders.add(order);
                }
            }

            orderMap.put(batchId, partAllotOrders);
        }

        boolean isSuc = process(orderMap, amount, note);
        if (isSuc) {
            return R.ok();
        } else {
            return LocalizedR.failed("nbTransferBatch.business.processing.failed", Optional.ofNullable(null));
        }
    }


    /**
     * 分页查询路区价格
     * @param page
     * @param pageVo
     * @return
     */
    @Override
    public Page<TransferBatchCostPageVo> pageTransferBatchCost(Page page, TransferBatchCostPageVo pageVo) {
        MPJLambdaWrapper<NbTransferBatchEntity> wrapper = new MPJLambdaWrapper<NbTransferBatchEntity>();
        wrapper.selectAll(NbTransferBatchEntity.class)
                .selectAs(NbDriverEntity::getDriverId, TransferBatchCostPageVo.Fields.driverId)
                .select(NbDriverEntity::getDriverName)
                .select(NbTransferCenterEntity::getTcId).selectAs(NbTransferCenterEntity::getCenterName, TransferBatchCostPageVo.Fields.transferCenterName)
                .select(NbSortingCenterEntity::getScId).selectAs(NbSortingCenterEntity::getCenterName, TransferBatchCostPageVo.Fields.sortingCenterName)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbTransferBatchEntity::getDriverId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbTransferBatchEntity::getTcId)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbTransferBatchEntity::getScId)
                .eq(ObjectUtil.isNotNull(pageVo.getDriverId()), NbTransferBatchEntity::getDriverId, pageVo.getDriverId())   //司机
                .eq(ObjectUtil.isNotNull(pageVo.getScId()), NbTransferBatchEntity::getScId, pageVo.getScId());              // 分拣中心

        // 创建时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        String addTime = pageVo.getCreateTime();
        if (StrUtil.isNotBlank(addTime)) {
            int splitIndex = addTime.indexOf(":", addTime.indexOf(":") + 1) + 3;
            String startTime = addTime.substring(0, splitIndex);
            String endTime = addTime.substring(splitIndex + 1);
            wrapper.ge(NbTransferBatchEntity::getAddTime, startTime).le(NbTransferBatchEntity::getAddTime, endTime);    // 创建时间
            wrapper.orderByDesc(NbTransferBatchEntity::getAddTime); //  根据创建时间倒序
        }

        // 查询司机收入
        BigDecimal incomeSubSidy = configUtil.findDecimalByKey(ConfigUtil.driver_income_subsidy);
        // 调Mapper层去查询并计算路区价格
        List<TransferBatchCostPageVo> voList = nbTransferBatchOrderMapper.listOrderCost(pageVo.getBatchId());
        Page<TransferBatchCostPageVo> voPage = new Page<TransferBatchCostPageVo>();

        voList.stream().forEach(vo -> {
            BigDecimal estimateIncome = BigDecimal.ZERO;
            BigDecimal estimatedHour = vo.getEstimatedHour();
            if (Objects.equals(estimatedHour, BigDecimal.ZERO)) {
                vo.setEstimatedHour(BigDecimal.ZERO); // 预估司机收入
                nbTransferBatchMapper.insert(vo);
            } else {
                // estimateIncome = new BigDecimal(vo.getEstimateAmount() / estimatedHour - incomeSubSidy).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                // vo.setEstimateIncome(estimateIncome); // 预估司机收入

                BigDecimal estimateAmount = Optional.ofNullable(vo.getEstimateAmount()).orElse(BigDecimal.ZERO);    // 获取估算金额并使用默认值
                // 计算 estimateIncome = (estimateAmount / estimatedHour) - incomeSubSidy
                estimateIncome = estimateAmount.divide(estimatedHour, 2, RoundingMode.HALF_UP)
                        .subtract(incomeSubSidy).setScale(2, RoundingMode.HALF_UP);
                vo.setEstimateIncome(estimateIncome); // 设置预估收入
            }
        });
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbTransferBatchEntity::getScId, idList);

        // TODO 导出收入操作 在拦截器TransferBatchCostInterceptor 新系统中，直接导出这些字段即可
        //	String uri = ac.ctrl.getRequest().getRequestURI();
        //	if (uri.contains("/grid/export")) {
        //		record.set("v_estimate_base", estimate_base);
        //		record.set("v_estimate_subsidy", estimate_subsidy);
        //		record.set("v_estimate_weight_subsidy", estimate_weight_subsidy);
        //		record.set("v_estimate_amount", estimate_amount);
        //		record.set("v_actual_base", actual_base);
        //		record.set("v_actual_subsidy", actual_subsidy);
        //		record.set("v_actual_weight_subsidy", actual_weight_subsidy);
        //		record.set("v_actual_amount", actual_amount);
        //
        //		if (estimatedHour == 0) {
        //			record.set("v_estimate_income", "NaN");
        //		} else {
        //			record.set("v_estimate_income", estimateIncome);
        //		}
        //	}
        return nbTransferBatchMapper.selectJoinPage(voPage, TransferBatchCostPageVo.class, wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean process(Map<Integer, List<NbOrderEntity>> orderMap, BigDecimal amount, String note) {
        Iterator<Map.Entry<Integer, List<NbOrderEntity>>> iter = orderMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<Integer, List<NbOrderEntity>> entry = iter.next();

            int batchId = entry.getKey();
            List<NbOrderEntity> orders = entry.getValue();
            int orderCount = orders.size();
            if (orderCount == 0) {
                log.warn("订单数量为0，跳过批次ID：" + batchId);
                continue;
            }
//            double perSubsidy = new BigDecimal(String.valueOf(amount)).divide(new BigDecimal(String.valueOf(orders.size())), 2, BigDecimal.ROUND_HALF_UP).doubleValue();
            BigDecimal perSubsidy = amount.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP); // 计算每单补贴金额 perSubsidy = amount / orderCount
            NbTransferBatchCostModifyLogEntity modifyLog = new NbTransferBatchCostModifyLogEntity();
            modifyLog.setBatchId(batchId);
            modifyLog.setAdminId(SecurityUtils.getUser().getId());
            modifyLog.setSubsidyAmount(amount);
            modifyLog.setAvgAmount(perSubsidy);
            modifyLog.setOrderTotal(orders.size());
            modifyLog.setAddTime(LocalDateTime.now());
            modifyLog.setNote(note);
            modifyLogService.save(modifyLog);

            for (NbOrderEntity order : orders) {
                NbOrderCostEntity oc = nbOrderCostService.getById(order.getOrderId());
                if (order.getHasSubOrder()) {
                    // 创建子订单的订单不能给补贴
                    if (oc.getDriverSubsidy().compareTo(BigDecimal.ZERO) > 0) {
                        oc.setDriverSubsidy(BigDecimal.ZERO);
                        nbOrderCostService.updateById(oc);
                    }
                    continue;
                }
                oc.setDriverSubsidy(perSubsidy);
                oc.setSubsidyLogId(modifyLog.getLogId());
                nbOrderCostService.updateById(oc);
            }
        }
        return true;
    }

    /**
     * 准备优化
     */
    @Override
    public R fixOptimization(String routeId, String optimizationProblemID) {
        Route route = route4MeUtil.getRoute(routeId);
        if (route == null) {
            return LocalizedR.failed("nbTransferBatch.Route.does.not.exist", routeId);
        }

        Address firstAddress = null;
        List<Address> addressList = route.getAddresses();
        for (Address address : addressList) {
            if (address.getDepot() == false) {
                firstAddress = address;
                break;
            }
        }

        Integer oneOfOrderId = Integer.valueOf(firstAddress.getCustom_fields().get("__orderId").toString());
        NbOrderEntity oneOfOrder = nbOrderMapper.selectById(oneOfOrderId);
        NbSortingCenterEntity sc = nbSortingCenterService.getById(oneOfOrder.getScId());
        String timezone = sc.getScTimezone();
        ZonedDateTime zonedDt = ZonedDateTime.now(ZoneId.of(timezone));
        String ymdStr = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
        String today = zonedDt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<R4mTobeUpdateRouter> tobeUpdateRouterList = new ArrayList<>();

        // "select count(1) cont from nb_transfer_batch where owner_sc_id = ? add_time >= ? and add_time <= ?", today + " 00:00:00", today + " 23:59:59", sc.getScId()).getInt("cont");
        LambdaQueryWrapper<NbTransferBatchEntity> wrapper = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
        wrapper.eq(NbTransferBatchEntity::getOwnerScId, sc.getScId())
                .ge(NbTransferBatchEntity::getAddTime, today + " 00:00:00")
                .le(NbTransferBatchEntity::getAddTime, today + " 23:59:59");
        int todayLQTotal = Math.toIntExact(count(wrapper));

//		List<Route> routes = optimization.getRoutes();
//		for (Route route : routes) {
        String r4mRouteId = route.getId();
        String roteView = route.getLinks().getView();
        int driverId = 0;

        if (route.getMemberId() != null && route.getMemberId() > 0) {
            Integer memberId = Math.toIntExact(route.getMemberId());

            //  select * from nb_driver where route4me_member_id = ? limit 1", memberId);
            LambdaQueryWrapper<NbDriverEntity> wrapper1 = Wrappers.lambdaQuery(NbDriverEntity.class);
            wrapper1.eq(NbDriverEntity::getRoute4meMemberId, memberId);
            NbDriverEntity driver = nbDriverMapper.selectOne(wrapper1);
            if (driver != null) {
                driverId = driver.getDriverId();
            }
        }

        // "select * from nb_transfer_batch where optimization_problem_id = ? and route_id = ? limit 1", route.getOptimizationProblemId(), routeId);
        LambdaQueryWrapper<NbTransferBatchEntity> qw = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
        qw.eq(NbTransferBatchEntity::getOptimizationProblemId, route.getOptimizationProblemId())
                .eq(NbTransferBatchEntity::getRouteId, r4mRouteId);
        NbTransferBatchEntity existTb = getOne(qw);
        if (existTb == null) {
            NbTransferBatchEntity tb = new NbTransferBatchEntity();
            tb.setBatchNo(sc.getPostalCode() + "-" + ymdStr + "-" + String.format("%02d", (todayLQTotal + 1)));
//				tb.setBatchNo(route.getParameters().getRouteName());
            tb.setAddTime(new Date());
            tb.setDriverId(driverId);
            tb.setOrderTotal(route.getRoutePieces());
//            tb.setEstimatedHour(new BigDecimal(route.getPlannedTotalRouteDuration() / 60 / 60.0d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            tb.setEstimatedHour(BigDecimal.valueOf(route.getPlannedTotalRouteDuration())
                    .divide(BigDecimal.valueOf(60), 10, RoundingMode.HALF_UP) // 转为小时，保留中间精度
                    .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP) // 转为小时并保留两位小数
            );

            if (driverId == 0) {
                tb.setGetType(2);
            } else {
                tb.setGetType(1);
            }

            tb.setBatchCode(tb.getBatchNo());
            tb.setTranferDriverId(0);
            tb.setTcId(0);
            tb.setRouteId(route.getId());
            tb.setOptimizationProblemId(optimizationProblemID);
            tb.setTripDistance(route.getTripDistance());
            tb.setPlannedTotalRouteDuration(route.getPlannedTotalRouteDuration());
            tb.setMemberId(route.getMemberId());
            tb.setRouteView(roteView);
//				tb.save();

            Parameters parameters = route.getParameters();
            parameters.setRouteName(tb.getBatchNo());
            route.setParameters(parameters);

            List<Address> routeAddressList = route.getAddresses();
            int index = 1;
            for (Address address : routeAddressList) {
                if (address.getDepot()) {
                    log.info("仓库跳过->" + address);
                    continue;
                }
                Integer orderId = Integer.valueOf(address.getCustom_fields().get("__orderId").toString());

                NbTransferBatchOrderEntity tbo = new NbTransferBatchOrderEntity();
                tbo.setBatchId(tb.getBatchId());
                tbo.setOrderId(orderId);

                tbo.setPickNo(today.split("-")[2] + "-" + String.format("%02d", (todayLQTotal + 1)) + "-" + String.format("%02d", address.getSequenceNo()));
                tbo.setRouteDestinationId(address.getRouteDestinationId());
                tbo.setMemberId(address.getMemberId().longValue());
                tbo.setRouteId(address.getRouteId());
                tbo.setOptimizationProblemId(address.getOptimizationProblemId());
                tbo.setSequenceNo(Math.toIntExact(address.getSequenceNo()));
                tbo.setChannelName(address.getChannelName());
                tbo.setDriveTimetoNextDestination(address.getDriveTimetoNextDestination());
                tbo.setTrackingNumber(address.getTrackingNumber());

                address.setAlias(tbo.getPickNo());

                NbOrderEntity order = nbOrderMapper.selectById(tbo.getOrderId());
                order.setPickNo(tbo.getPickNo());
                if (driverId > 0) {
                    order.setDriverId(driverId);
                }

                index++;
            }

            tobeUpdateRouterList.add(new R4mTobeUpdateRouter(route));
            todayLQTotal++;
        }

//		}
        return R.ok();
    }

    /**
     * 列出路区
     */
    @Override
    public R list(Integer batchId) {
        // "select * from nb_transfer_batch where is_delete = false and order_batch_id = ? order by batch_no asc", batchId);
        LambdaQueryWrapper<NbTransferBatchEntity> wrapper = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
        wrapper.eq(NbTransferBatchEntity::getOrderBatchId, batchId)
                .eq(NbTransferBatchEntity::getIsDelete, false)
                .orderByAsc(NbTransferBatchEntity::getBatchNo);
        List<NbTransferBatchEntity> batchs = list(wrapper);
        JSONArray ja = batchs.stream().map(b -> {
            JSONObject jo = new JSONObject();
            jo.set("batchId", b.getBatchId());
            jo.set("batchNo", b.getBatchNo());
            jo.set("driverId", b.getDriverId());
            jo.set("orderTotal", b.getOrderTotal());
            jo.set("estimatedHour", b.getEstimatedHour());

            if (b.getTcId() > 0) {
                NbTransferCenterEntity tc = nbTransferCenterMapper.selectById(b.getTcId());
                JSONObject tcjo = new JSONObject();
                tcjo.set("tcId", tc.getTcId());
                tcjo.set("code", tc.getTransferCenterCode());
                tcjo.set("provinceId", tc.getProvinceId());
                tcjo.set("province", commonDataUtil.getProvinceById(tc.getProvinceId()));
                tcjo.set("cityId", tc.getCityId());
                tcjo.set("city", commonDataUtil.getCityById(tc.getCityId()));
                tcjo.set("address", tc.getAddress());
                tcjo.set("postalCode", tc.getPostalCode());
                tcjo.set("centerName", tc.getCenterName());
                jo.set("tc", tcjo);
            }
            if (b.getScId() > 0) {
                NbSortingCenterEntity sc = nbSortingCenterService.getById(b.getScId());
                JSONObject scjo = new JSONObject();
                scjo.set("scId", sc.getScId());
                scjo.set("centerName", sc.getCenterName());
                scjo.set("address", sc.getAddress());
                scjo.set("postalCode", sc.getPostalCode());
                scjo.set("country", commonDataUtil.getCountryById(sc.getCountryId()));
                scjo.set("province", commonDataUtil.getProvinceById(sc.getProvinceId()));
                scjo.set("city", commonDataUtil.getCityById(sc.getCityId()));
                scjo.set("scCode", sc.getScCode());
                jo.set("sc", scjo);
            }

            // "select o.* from nb_transfer_batch_order tbo, nb_order o where tbo.order_id = o.order_id and tbo.batch_id = ?", b.getBatchId());
            MPJLambdaWrapper<NbTransferBatchOrderEntity> wrapper1 = new MPJLambdaWrapper<>();
            wrapper1.selectAll(NbOrderEntity.class)
                    .eq(NbTransferBatchOrderEntity::getBatchId, b.getBatchId())
                    .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbTransferBatchOrderEntity::getOrderId);
            List<NbOrderEntity> orderList = nbTransferBatchOrderMapper.selectJoinList(NbOrderEntity.class, wrapper1);

            JSONArray orderJa = orderList.stream().map(new OrderDto()::toDriverListJson).collect(Collectors.toCollection(JSONArray::new));
            jo.set("orders", orderJa);

            if (b.getDriverId() > 0) {
                NbDriverEntity driver = nbDriverMapper.selectById(b.getDriverId());
                JSONObject driverjo = new DriverDto().toAppJson(driver);
                jo.set("driver", driverjo);
            }

            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));

        return R.ok(ja);
    }

    /**
     * 删除路区
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R remove(Integer batchId) {
        NbTransferBatchEntity tb = getById(batchId);
        if (tb == null) {
            return LocalizedR.failed("nbTransferBatch.Route.does.not.exist", Optional.ofNullable(null));
        }

        if (tb.getStartTime() != null) {
            return LocalizedR.failed("nbTransferBatch.Delivery.routes.that.have.already.been.activated.no.delete", Optional.ofNullable(null));
        }

        if (tb.getIsDelete()) {
            return LocalizedR.failed("nbTransferBatch.the.route.has.been.deleted.and.the.time", DateFormatUtils.format(tb.getDeleteTime(), "yyyy-MM-dd HH:mm:ss"));
        }

        // "select o.* from nb_transfer_batch_order tbo, nb_order o where tbo.order_id = o.order_id and tbo.batch_id = ? and o.order_status > 200 limit 1", batchId);
        NbOrderEntity statusInvalidOrder = nbOrderMapper.findTboByBatchIdAndStatus(batchId, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
        if (statusInvalidOrder != null) {
            return LocalizedR.failed("nbTransferBatch.the.route.no.deleted.and.the.status", statusInvalidOrder.getOrderStatus());
        }

        R r = route4MeUtil.removeRoute(tb.getRouteId());
        if (r.isOk()) {
            RouteDeletedResponse response = (RouteDeletedResponse) r.getData();
            if (response.isDeleted() == false) {
                return LocalizedR.failed("nbTransferBatch.deleted.failed", Optional.ofNullable(null));
            }
        } else {
            return LocalizedR.failed("nbTransferBatch.deleted.failed.msg", r.getMsg());
        }

        tb.setIsDelete(true);
        tb.setDeleteTime(new Date());
        tb.setDeleteUserId(SecurityUtils.getUser().getId());
        nbTransferBatchMapper.updateById(tb);

        Set<Integer> tcIds = Sets.newHashSet();
        // "select o.* from nb_transfer_batch_order tbo, nb_order o where tbo.order_id = o.order_id and tbo.batch_id = ?", batchId);
        MPJLambdaWrapper<NbTransferBatchOrderEntity> wrapper1 = new MPJLambdaWrapper<>();
        wrapper1.selectAll(NbOrderEntity.class)
                .eq(NbTransferBatchOrderEntity::getBatchId, batchId)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbTransferBatchOrderEntity::getOrderId);
        List<NbOrderEntity> orders = nbTransferBatchOrderMapper.selectJoinList(NbOrderEntity.class, wrapper1);
        for (NbOrderEntity order : orders) {
            order.setIsRouted(false);
            order.setPickNo("");
            nbOrderMapper.updateById(order);

            log.info("订单恢复未规划状态:" + order.getPkgNo() + ",orderId=" + order.getOrderId());

            tcIds.add(order.getTcId());
        }

        // "select * from nb_transfer_batch_order where batch_id = ?", batchId);
        LambdaQueryWrapper<NbTransferBatchOrderEntity> wrapper = Wrappers.lambdaQuery(NbTransferBatchOrderEntity.class);
        List<NbTransferBatchOrderEntity> tbos = nbTransferBatchOrderMapper.selectList(wrapper.eq(NbTransferBatchOrderEntity::getBatchId, batchId));
        for (NbTransferBatchOrderEntity tbo : tbos) {
            log.info("删除路区节点:" + tbo);
            nbTransferBatchOrderMapper.deleteById(tbo);
        }

        tcIds.forEach(tcId -> {
            // "select * from nb_r4m_optimizationlog where order_batch_id = ? and tc_id = ? and r4m_state = 4 limit 1", tb.getOrderBatchId(), tcId);
            LambdaQueryWrapper<NbR4mOptimizationLogEntity> qw = Wrappers.lambdaQuery(NbR4mOptimizationLogEntity.class);
            qw.eq(NbR4mOptimizationLogEntity::getOrderBatchId, tb.getOrderBatchId())
                    .eq(NbR4mOptimizationLogEntity::getTcId, tcId)
                    .eq(NbR4mOptimizationLogEntity::getR4mState, 4);
            NbR4mOptimizationLogEntity optimizationLog = nbR4mOptimizationLogMapper.selectOne(qw);
            if (optimizationLog != null) {
                optimizationLog.setR4mState(100);
                nbR4mOptimizationLogMapper.updateById(optimizationLog);

                log.info("规划记录状态修改:" + log);
            }
        });

        NbOrderBatchEntity ob = nbOrderBatchMapper.selectById(tb.getOrderBatchId());
        if (ob.getIsRouted() == true) {
            ob.setIsRouted(false);
            nbOrderBatchMapper.updateById(ob);
        }

        return R.ok();
    }

    /*
     * 同步规划完的路区-同步所有路区
     */
    @Override
    public R syncRoutes() {
        int pageNo = 1;
        int pageSize = 10;
        boolean hasMore = true;

        if (isSyncRoute) {
            R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
            SysUser user = sysUserR.getData();
            String username = user.getUsername();
            WebSocketMessageSender.send(username, "正在进行同步，当前查看到的同步记录，为另一端正在进行的同步日志");
            return R.ok();
        }
        isSyncRoute = true;
        long start = System.currentTimeMillis();
        try {
            Map<Integer, NbSortingCenterEntity> scMap = findSyncScForCurAccount();
            if (scMap == null || scMap.size() == 0) {
                return LocalizedR.failed("nbTransferBatch.not.sortingcenter.configured.cannot.perform.route.synchronization", Optional.ofNullable(null));
            }
            wsMessage("开始同步，同步范围：" + scMap.values().stream().map(NbSortingCenterEntity::getScCode).collect(Collectors.joining("、")));

            int existCount = 0;
            while (hasMore) {
                List<Route> routes = route4MeUtil.getRoutes(pageNo, pageSize);

                wsMessage("第" + pageNo + "页， 检测到" + routes.size() + "个Route");

                for (Route route : routes) {
                    wsMessage("开始处理route[" + route.getId() + "]");
                    if (route.isUnrouted()) {
                        wsMessage("route[" + route.getId() + "]为UNROUTED状态，跳过");
                        continue;
                    }
                    String routeId = route.getId();
                    // select * from nb_transfer_batch where route_id = ? limit 1
                    LambdaQueryWrapper<NbTransferBatchEntity> wrapper = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
                    NbTransferBatchEntity tb = nbTransferBatchMapper.selectOne(wrapper.eq(NbTransferBatchEntity::getRouteId, routeId));
                    if (tb == null) {
                        log.info("开始处理路区:" + route + "->"  + (System.currentTimeMillis() - start));
                        Route routeDetail = route4MeUtil.getRoute(route.getId());
                        log.info("获取路区结束：" + (System.currentTimeMillis() - start));

                        if (routeDetail == null) {
                            wsMessage("路区获取失败:" + route.getId() + "->" + route.getParameters().getRouteName());
                            log.info("路区获取失败:" + route.getId() + "->" + route.getParameters().getRouteName());
                            continue;
                        }
                        dealRoute(scMap, routeDetail); // 只同步自己区域内的线路
                        log.info("处理路区结束："  + (System.currentTimeMillis() - start));

                        existCount = 0;
                    } else {
                        existCount ++;

                        log.info("路区存在,跳过:" + route);
                        wsMessage("路区存在,跳过:" + route);
                    }

                    try {
                        //  select * from nb_r4m_route_createlog where route_id = ? limit 1
                        LambdaQueryWrapper<NbR4mRouteCreateLogEntity> qw = Wrappers.lambdaQuery(NbR4mRouteCreateLogEntity.class);
                        qw.eq(NbR4mRouteCreateLogEntity::getRouteId, routeId);
                        NbR4mRouteCreateLogEntity log = nbR4mRouteCreateLogMapper.selectOne(qw);
                        if (log != null && log.getStatus() != R4mRouteCreateLogDto.STATUS_2_SYNCED) {
                            log.setSynbTime(Instant.now().toEpochMilli());
                            log.setStatus(R4mRouteCreateLogDto.STATUS_2_SYNCED);
                            log.setUpdateTime(Instant.now().toEpochMilli());
                            nbR4mRouteCreateLogMapper.updateById(log);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
//                        ExceptionKit.handler(e, "/transferBatch/syncRoutes/updateRouteCreateLog", this);
                    }
                }

                if (existCount >= 30 || routes.size() == 0) {
                    wsMessage("查询没有待同步路区，结束");
                    hasMore = false;
                }

                pageNo++;
            }

            try {
                // "select * from nb_r4m_route_createlog where status = ?", R4mRouteCreateLog.STATUS_1_CREATE);
                LambdaQueryWrapper<NbR4mRouteCreateLogEntity> qw = Wrappers.lambdaQuery(NbR4mRouteCreateLogEntity.class);
                List<NbR4mRouteCreateLogEntity> logs = nbR4mRouteCreateLogMapper.selectList(qw.eq(NbR4mRouteCreateLogEntity::getStatus, R4mRouteCreateLogDto.STATUS_1_CREATE));

                for (NbR4mRouteCreateLogEntity createLog : logs) {
                    log.info("补充路区routeId=" + createLog.getRouteId());
                    String routeId = createLog.getRouteId();
                    // select * from nb_transfer_batch where route_id = ? limit 1
                    LambdaQueryWrapper<NbTransferBatchEntity> wrapper = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
                    NbTransferBatchEntity tb = nbTransferBatchMapper.selectOne(wrapper.eq(NbTransferBatchEntity::getRouteId, routeId));
                    if (tb == null) {
                        log.info("开始处理路区:" + routeId);

                        Route routeDetail = route4MeUtil.getRoute(routeId);
                        if (routeDetail == null) {
                            createLog.setStatus(R4mRouteCreateLogDto.STATUS_10_DELETED);
                        } else {
                            dealRoute(scMap, routeDetail);   // 只同步自己区域内的线路
                            createLog.setStatus(R4mRouteCreateLogDto.STATUS_2_SYNCED);
                        }
                    } else {
                        createLog.setStatus(R4mRouteCreateLogDto.STATUS_4_EXIST);
                        createLog.setTransferBatchId(tb.getBatchId());
                    }

                    createLog.setSynbTime(Instant.now().toEpochMilli());
                    createLog.setUpdateTime(Instant.now().toEpochMilli());
                    nbR4mRouteCreateLogMapper.updateById(createLog);

                }
            } catch (Exception e) {
                e.printStackTrace();
//                ExceptionKit.handler(e, "/transferBatch/syncRoutes/updateRouteCreateLog_his", this);
            }
        } finally {
            isSyncRoute = false;
        }

        return R.ok();
    }

    /**
     * 确认好路区没问题，往route4me同步线路号
     * @param batchId 路区ID
     * @return
     */
    @Override
    public R syncLineNo(Integer batchId) {
        NbTransferBatchEntity tb = getById(batchId);
        if (tb == null) {
            return LocalizedR.failed("nbTransferBatch.the.road.area.does.not.exist", Optional.ofNullable(null));
        }

        Route route = route4MeUtil.getRoute(tb.getRouteId());
        if (route == null){
            return LocalizedR.failed("nbTransferBatch.the.road.area.does.not.exist", Optional.ofNullable(null));
        }
        for (Address address : route.getAddresses()) {
            if (address.getDepot()) {
                continue;
            }
            // "select * from nb_transfer_batch_order where batch_id = ? and route_destination_id = ? limit 1", batchId, address.getRouteDestinationId());
            if (ObjectUtil.isNotNull(address.getRouteDestinationId())) {
                NbTransferBatchOrderEntity tbo = nbTransferBatchOrderMapper.selectOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>().eq(NbTransferBatchOrderEntity::getBatchId, batchId)
                        .eq(NbTransferBatchOrderEntity::getRouteDestinationId, address.getRouteDestinationId()), false);
                if (tbo != null) {
                    address.setAlias(tbo.getPickNo());
                } else {
                    return LocalizedR.failed("nbTransferBatch.transferBatchOrder.not.found", tbo.getOrderId());
                }
                NbOrderEntity order = nbOrderMapper.selectById(tbo.getOrderId());
                route4MeUtil.updateAddressAttribute(route.getId(), address.getRouteDestinationId(), address, order);

                log.info("路区" + route.getId() + "订单" + order.getPkgNo() + "别名更新：" + address.getAlias());
            } else {
                return LocalizedR.failed("nbTransferBatch.routeDestinationId.does.not.exist", address.getRouteDestinationId());
            }
        }

        return R.ok();
    }

    //  只同步自己区域内的线路
    private void dealRoute(Map<Integer, NbSortingCenterEntity> scMap, Route route) {
        List<Address> addressList = route.getAddresses();
        wsMessage("路区" + route.getId() +"名字为：" + route.getParameters().getRouteName() + ",共" + addressList.size() + "个节点");
        NbSortingCenterEntity sc = null;
        NbSortingCenterEntity startSc = null;
        NbTransferCenterEntity startTc = null;
        NbOrderBatchEntity orderBatch = null;

        // 判断所属分拣中心
        if (sc == null) {
            for (Address address : addressList) {
                if (address.getDepot()) {
                    Map<String, Object> customFields = address.getCustom_fields();

                    if (customFields.containsKey("type")) {
                        String type = customFields.get("type").toString();
                        if (type == "sc") {
                            String scCode = customFields.get("scCode").toString();
                            // "select * from nb_soring_center where sc_code = ? limit 1", scCode);
                            LambdaQueryWrapper<NbSortingCenterEntity> wrapper = Wrappers.lambdaQuery(NbSortingCenterEntity.class);
                            startSc = nbSortingCenterService.getOne(wrapper.eq(NbSortingCenterEntity::getScCode, scCode));
                        } else if (type == "tc") {
                            String tcCode = customFields.get("tcCode").toString();
                            // "select * from nb_transfer_center where transfer_center_code = ? limit 1", tcCode);
                            LambdaQueryWrapper<NbTransferCenterEntity> qw = Wrappers.lambdaQuery(NbTransferCenterEntity.class);
                            startTc = nbTransferCenterMapper.selectOne(qw.eq(NbTransferCenterEntity::getTransferCenterCode, tcCode));
                        }
                    }
                    continue;
                }

                NbOrderEntity order;
                Integer r4mOrderId = address.getOrderId();
                if (r4mOrderId == null) {
                    Map<String, Object> customFields = address.getCustom_fields();
                    if (customFields.size() == 0) {
                        continue;
                    }

                    if (!customFields.containsKey("__orderId")) {
                        String msg = "同步路区[" + route.getParameters().getRouteName() + "]时，订单没有找到：r4mOrderId=" + r4mOrderId;
                        log.info(msg);
                        wsMessage(msg);
                        continue;
                    }

                    Integer orderId = Integer.valueOf(customFields.get("__orderId").toString());
                    order = nbOrderMapper.selectById(orderId);
                } else {
                    // "select * from nb_order where r4m_order_id = ? limit 1", r4mOrderId);
                    LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                    order = nbOrderMapper.selectOne(wrapper.eq(NbOrderEntity::getR4mOrderId, r4mOrderId));
                }

                if (order == null) {
                    wsMessage("订单没有找到：r4mOrderId=" + r4mOrderId + ",orderId=" + address.getCustom_fields().get("__orderId"));
                    continue;
                }

                sc = nbSortingCenterService.getById(order.getScId());
                String subBatchNo = order.getSubBatchNo();
                // select * from nb_order_batch where batch_no = ? limit 1", subBatchNo);
                LambdaQueryWrapper<NbOrderBatchEntity> wrapper = Wrappers.lambdaQuery(NbOrderBatchEntity.class);
                orderBatch = nbOrderBatchMapper.selectOne(wrapper.eq(NbOrderBatchEntity::getBatchNo, subBatchNo));
                break;
            }
        }

        if (sc == null) {
            log.info("同步route时,无法找到订单对应的分拣中心,同步失败:routeId=" + route.getId());

            wsMessage("同步route时,无法找到订单对应的分拣中心,同步失败:routeId=" + route.getId());
            return;
        }

        String routeView = route.getLinks().getRoute();
        int scId = sc.getScId();

        if (!scMap.containsKey(scId)) {
            String msg = "路区" + route.getId() +"名字为：" + route.getParameters().getRouteName() + ",不是当前账号区域内路区，跳过同步";
            log.info(msg);
            wsMessage(msg);
            return;
        }

        Long createTimestampSecond = route.getCreated_timestamp();
        String timezone = sc.getScTimezone();
        String routeName = sc.getScCode();

        OffsetDateTime createDatatime = OffsetDateTime.ofInstant(Instant.ofEpochSecond(createTimestampSecond), ZoneOffset.of(timezone));
        OffsetDateTime routeNocreateDatatime = createDatatime.minusHours(12);

        String today = null;
        String yyMMdd = null;

        today = routeNocreateDatatime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        yyMMdd = routeNocreateDatatime.format(DateTimeFormatter.ofPattern("yyMMdd"));

        Long startTimestamp = 0l, endTimestamp = 0l;
        int currentHour = createDatatime.getHour();
        if (currentHour >= 12) {
            OffsetDateTime dayOfStart = createDatatime.withHour(12).withMinute(0).withSecond(0).withNano(0);
            startTimestamp = dayOfStart.toInstant().toEpochMilli();

            OffsetDateTime dayOfEnd = createDatatime.plusDays(1).withHour(11).withMinute(59).withSecond(59).withNano(999999999);
            endTimestamp = dayOfEnd.toInstant().toEpochMilli();
        } else {
            OffsetDateTime dayOfStart = createDatatime.minusDays(1).withHour(12).withMinute(0).withSecond(0).withNano(0);
            startTimestamp = dayOfStart.toInstant().toEpochMilli();

            OffsetDateTime dayOfEnd = createDatatime.withHour(11).withMinute(59).withSecond(59).withNano(999999999);
            endTimestamp = dayOfEnd.toInstant().toEpochMilli();
        }

        // "select count(1) cont from nb_transfer_batch where owner_sc_id = ? and add_timestamp >= ? and add_timestamp <= ?", scId, startTimestamp, endTimestamp).getInt("cont");
        LambdaQueryWrapper<NbTransferBatchEntity> wrapper = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
        Long count = nbTransferBatchMapper.selectCount(wrapper.eq(NbTransferBatchEntity::getOwnerScId, scId).ge(NbTransferBatchEntity::getAddTimestamp, startTimestamp).le(NbTransferBatchEntity::getAddTimestamp, endTimestamp));
        int todayLQTotal = Math.toIntExact(count);
        routeName += ("-" + yyMMdd) + "-" + String.format("%02d", (todayLQTotal + 1));

        int driverId = 0;
        String assignVehicleId = null;
        Vehicles vehicles = route.getVehicle();
        if (vehicles == null) {
            log.info("路区" + route.getId() + "没有车辆,回传");
            wsMessage("路区" + route.getId() + "监测到没有分配车辆,正在匹配司机对应的车辆");

            Long memberId = route.getMemberId();
            NbDriverEntity driver = null;
            if (memberId != null && memberId > 0) {
                // "select * from nb_driver where route4me_member_id = ? limit 1", memberId);
                LambdaQueryWrapper<NbDriverEntity> wrapper1 = Wrappers.lambdaQuery(NbDriverEntity.class);
                wrapper1.eq(NbDriverEntity::getRoute4meMemberId, memberId);
                driver = nbDriverMapper.selectOne(wrapper1);
                if (driver != null) {
                    driverId = driver.getDriverId();
                }
            }

            if (driver != null) {
                String vehicleId = driver.getRoute4meVehicleId();

                if (StrUtil.isBlank(vehicleId)) {
                    log.info("路区" + route.getId() + "监测到没有分配车辆,司机:" + driver.getDriverId() + ",没有车辆信息,无法完成分配");
                    wsMessage("路区" + route.getId() + "监测到没有分配车辆,司机:" + driver.getDriverId() + ",没有车辆信息,无法完成分配");

                } else {
                    log.info("路区" + route.getId() + "监测到没有分配车辆,司机:" + driver.getDriverId() + "的车辆" + vehicleId + "分配中");
                    wsMessage("路区" + route.getId() + "监测到没有分配车辆,司机:" + driver.getDriverId() + "的车辆" + vehicleId + "分配中");
                    route4MeUtil.assignVehicle(route.getId(), vehicleId);

                    assignVehicleId = vehicleId;
                }
            }
        } else {
            assignVehicleId = vehicles.getVehicleId();

            // "select * from nb_driver where route4me_vehicle_id = ? limit 1", assignVehicleId);
            LambdaQueryWrapper<NbDriverEntity> qw = Wrappers.lambdaQuery(NbDriverEntity.class);
            qw.eq(NbDriverEntity::getRoute4meVehicleId, assignVehicleId);
            NbDriverEntity driver = nbDriverMapper.selectOne(qw);
            if (driver == null) {
                Long memberId = route.getMemberId();
                if (memberId != null && memberId > 0) {
                    // "select * from nb_driver where route4me_member_id = ? limit 1", memberId);
                    LambdaQueryWrapper<NbDriverEntity> qw1 = Wrappers.lambdaQuery(NbDriverEntity.class);
                    qw1.eq(NbDriverEntity::getRoute4meMemberId, memberId);
                    driver = nbDriverMapper.selectOne(qw1);
                    if (driver != null) {
                        driverId = driver.getDriverId();
                    }
                }
            } else {
                driverId = driver.getDriverId();
                route4MeUtil.assignDriver(route.getId(), driver.getRoute4meMemberId().toString());
            }
        }

        wsMessage("路区" + route.getId() + "系统重新分配名称为：" + routeName);

        NbTransferBatchEntity tb = new NbTransferBatchEntity();
        tb.setVehicleId(assignVehicleId);
        tb.setBatchNo(routeName);
        tb.setAddTime(Date.from(createDatatime.toInstant()));
        tb.setAddTimestamp(createTimestampSecond * 1000);
        tb.setDriverId(driverId);
        tb.setOrderTotal(route.getRoutePieces());

//        tb.setEstimatedHour(new BigDecimal(route.getPlannedTotalRouteDuration() / 60 / 60.0d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        BigDecimal plannedDuration = BigDecimal.valueOf(route.getPlannedTotalRouteDuration());
        BigDecimal estimatedHour = plannedDuration.divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP); // 将总秒数转换为小时，保留两位小数
        tb.setEstimatedHour(estimatedHour);
        try {
//            int hour = route.getPlannedTotalRouteDuration() / 3600;
//            int min = (route.getPlannedTotalRouteDuration() % 3600) / 60;
//            Double hour2 = Double.valueOf(hour + "." + min);
//            tb.setEstimatedHour(hour2);
            BigDecimal[] hourAndMinute = plannedDuration.divideAndRemainder(BigDecimal.valueOf(3600)); // 小时部分和剩余秒数
            BigDecimal hour = hourAndMinute[0]; // 小时部分
            BigDecimal minute = hourAndMinute[1].divide(BigDecimal.valueOf(60), 0, RoundingMode.FLOOR); // 转换为整分钟

            // 合并小时和分钟为小数形式（例如：2.15 表示 2 小时 15 分钟）
            BigDecimal hourWithMinute = hour.add(minute.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            tb.setEstimatedHour(hourWithMinute);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (driverId == 0) {
            tb.setGetType(TransferBatchDto.GET_TYPE_2_SNATCH);
        } else {
            tb.setGetType(TransferBatchDto.GET_TYPE_1_ALLOCATION);
        }

        tb.setBatchCode(tb.getBatchNo());
        tb.setTranferDriverId(0);
        if (startTc != null) {
            tb.setTcId(startTc.getTcId());
        }
        if (startSc != null) {
            tb.setScId(startSc.getScId());
        }
        tb.setRouteId(route.getId());
        tb.setOptimizationProblemId(route.getOptimizationProblemId());
        tb.setTripDistance(route.getTripDistance());
        tb.setPlannedTotalRouteDuration(route.getPlannedTotalRouteDuration());
        tb.setMemberId(route.getMemberId());
        tb.setRouteView(routeView);
        tb.setOwnerScId(scId);
        if (startSc == null) {
            tb.setScId(scId);
        }
        if (orderBatch != null) {
            tb.setOrderBatchId(orderBatch.getBatchId());
        }
        save(tb);

        Parameters parameters = route.getParameters();
        parameters.setRouteName(tb.getBatchNo());
        route.setParameters(parameters);

        Map<Long, NbOrderEntity> orderMap = new HashMap<>();

        int orderCount = 0;
        for (Address address : addressList) {
            if (address.getDepot()) {
                log.info("同步路区,地址为仓库,跳过: alias=" + address.getAlias() + "," + address.getRouteDestinationId());
                continue;
            }

            if (address.getDepot()) {
                log.info("仓库跳过->" + address);
                continue;
            }

            int sequenceNo = Math.toIntExact(address.getSequenceNo());

            Integer r4mOrderId = address.getOrderId();
            Integer orderId;
            if (r4mOrderId == null) {
                Map<String, Object> customFields = address.getCustom_fields();
                if (customFields.size() == 0) {
                    log.info("同步路区,地址无自定义属性,跳过: alias=" + address.getAlias() + "," + address.getRouteDestinationId());
                    continue;
                }

                orderId = Integer.valueOf(address.getCustom_fields().get("__orderId").toString());
            } else {
                // "select * from nb_order where r4m_order_id = ? limit 1", r4mOrderId);
                LambdaQueryWrapper<NbOrderEntity> wp = Wrappers.lambdaQuery(NbOrderEntity.class);
                wp.eq(NbOrderEntity::getR4mOrderId, r4mOrderId);
                NbOrderEntity order = nbOrderMapper.selectOne(wp);
                if (order == null) {
                    log.info("同步路区,地址无自定义属性，且通过内部__orderId也无法识别,跳过: alias=" + address.getAlias() + "," + address.getRouteDestinationId());
                    continue;
                }
                orderId = order.getOrderId();
            }

            String pickNo = today.split("-")[2] + "-" + String.format("%02d", (todayLQTotal + 1)) + "-" + String.format("%02d", sequenceNo);

            NbTransferBatchOrderEntity tbo = new NbTransferBatchOrderEntity();
            tbo.setBatchId(tb.getBatchId());
            tbo.setOrderId(orderId);
            tbo.setPickNo(pickNo);
            tbo.setRouteDestinationId(address.getRouteDestinationId());
            tbo.setMemberId(address.getMemberId().longValue());
            tbo.setRouteId(address.getRouteId());
            tbo.setOptimizationProblemId(address.getOptimizationProblemId());
            tbo.setSequenceNo(Math.toIntExact(address.getSequenceNo()));
            tbo.setChannelName(address.getChannelName());
            tbo.setDriveTimetoNextDestination(address.getDriveTimetoNextDestination());
            tbo.setTrackingNumber(address.getTrackingNumber());
            nbTransferBatchOrderMapper.insert(tbo);

            address.setAlias(pickNo);

            NbOrderEntity order = nbOrderMapper.selectById(tbo.getOrderId());
            order.setPickNo(tbo.getPickNo());
            if (driverId > 0) {
                order.setDriverId(driverId);
            }
            order.setIsRouted(true);
            nbOrderMapper.updateById(order);

            orderMap.put(address.getRouteDestinationId(), order);

            orderCount ++;
        }

        if (tb.getOrderTotal() != orderCount) {
            tb.setOrderTotal(orderCount);
            nbTransferBatchMapper.updateById(tb);
        }

        List<R4mTobeUpdateRouter> tobeUpdateRouterList = Lists.newArrayList();
        tobeUpdateRouterList.add(new R4mTobeUpdateRouter(route));

        wsMessage("路区" + route.getId() + "更新数据等待完成...");

        wsData(route.getId(), routeName, route.getAddresses().size());

        Route updateRoute = new Route();
        updateRoute.setId(route.getId());

        Parameters updatePara = new Parameters();
        updatePara.setRouteName(routeName);
        updateRoute.setParameters(updatePara);
        route4MeUtil.updateRoute(updateRoute);

        ExecutorService executorService = Executors.newFixedThreadPool(10);

        for (Address updateAddressFull: addressList) {
            Address updateAddr = new Address();
            if (updateAddressFull.getDepot()) {
                continue;
            }

            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    if (orderMap.containsKey(updateAddressFull.getRouteDestinationId())) {
                        NbOrderEntity order = orderMap.get(updateAddressFull.getRouteDestinationId());

                        updateAddr.setRouteDestinationId(updateAddressFull.getRouteDestinationId());
                        updateAddr.setAlias(updateAddressFull.getAlias());

                        route4MeUtil.updateAddressAttribute(route.getId(), updateAddressFull.getRouteDestinationId(), updateAddr, order);

                        log.info("路区" + route.getId() + "订单" + order.getPkgNo() + "别名更新：" + updateAddressFull.getAlias());
                        wsMessage("路区" + route.getId() + "订单" + order.getPkgNo() + "别名更新：" + updateAddressFull.getAlias());
                    }
                }
            });

        }
        executorService.shutdown();

        while(true) {
            if(executorService.isTerminated()) {
                break;

            } else {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        todayLQTotal++;
    }

    private void wsData(String id, String routeName, int addressSize) {
        try {
            com.alibaba.fastjson.JSONObject wsData = new com.alibaba.fastjson.JSONObject();
            wsData.put("type", "data");
            wsData.put("time", Instant.now().toEpochMilli());
            wsData.put("routeId", id);
            wsData.put("routeName", routeName);
            wsData.put("addressSize", addressSize);

//            Tio.sendToGroup(WsKit.use(), "_syncRoutes", WsResponse.fromText(wsData.toString(), "UTF-8"));
            R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
            SysUser user = sysUserR.getData();
            String username = user.getUsername();
            WebSocketMessageSender.send(username, wsData.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void wsMessage(String message) {
        try {
            JSONObject wsData = new JSONObject();
            wsData.set("type", "message");
            wsData.set("time", Instant.now().toEpochMilli());
            wsData.set("message", message);

//            Tio.sendToGroup(WsKit.use(), "_syncRoutes", WsResponse.fromText(wsData.toString(), "UTF-8"));
            R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
            SysUser user = sysUserR.getData();
            String username = user.getUsername();
            WebSocketMessageSender.send(username, wsData.toString());
        } catch (Exception e) {
            e.printStackTrace();

        }
    }

    /**
     * 返回当前账户可同步的区域
     * @return
     */
    private Map<Integer, NbSortingCenterEntity> findSyncScForCurAccount() {
        R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
        SysUser sysUser = sysUserR.getData();
        String nbscid = sysUser.getNbdScId();
        if (StrUtil.isBlank(nbscid)) {
            LocalizedR.failed("nbTransferBatch.not.sortingcenter.configured.cannot.perform.route.synchronization", Optional.ofNullable(null));
            return null ;
        }

        String[] scidArr = nbscid.split(",");
        Map<Integer, NbSortingCenterEntity> scMap = new HashMap<>();

        for (String scid : scidArr) {
            NbSortingCenterEntity sc = nbSortingCenterService.getById(Integer.valueOf(scid));
            if (sc != null) {
                scMap.put(Integer.valueOf(scid), sc);
            }
        }

        return scMap;
    }

    @Override
    public NbTransferBatchEntity selectJoinOne(Class<NbTransferBatchEntity> nbTransferBatchEntityClass, MPJLambdaWrapper<NbTransferBatchEntity> wrapper) {
        return nbTransferBatchMapper.selectJoinOne(nbTransferBatchEntityClass, wrapper);
    }

    /**
     * 同步一个路区
     * @param routeId
     * @return
     */
    @Override
    public R syncOneRoutes(String routeId) {
        NbTransferBatchEntity tb = nbTransferBatchMapper.selectOne(new LambdaQueryWrapper<NbTransferBatchEntity>().eq(NbTransferBatchEntity::getRouteId, routeId));
        if (tb == null) {
			Map<Integer, NbSortingCenterEntity> scMap = findSyncScForCurAccount();
			if (scMap == null || scMap.size() == 0) {
				return LocalizedR.failed("nbTransferBatch.not.sortingcenter.configured.cannot.perform.route.synchronization", Optional.ofNullable(null));
			}

			Route route = route4MeUtil.getRoute(routeId);
			if (route == null) {
				return LocalizedR.failed("nbTransferBatch.route.not.found", Optional.ofNullable(null));
			}
			dealRoute(scMap, route);
		}

		return R.ok("同步成功：" + tb.getBatchCode());
    }

    // 查询当前用户分拣中心下的转运中心的司机
    @Override
    public Page<NbDriverEntity> pageAllDriver(Page page, NbDriverEntity entity) {
        MPJLambdaWrapper<NbDriverEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbDriverEntity.class);
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbDriverEntity::getScId, idList);
        return nbDriverMapper.selectPage(page, wrapper);
    }

    // 路区导出Excel
    @Override
    public List<NbTransferBatchExcelVo> getExcel(NbTransferBatchPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        return nbTransferBatchMapper.selectJoinList(NbTransferBatchExcelVo.class, wrapper);
    }

    // 分页查询条件
    private MPJLambdaWrapper getWrapper(NbTransferBatchPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbTransferBatchEntity> wrapper = new MPJLambdaWrapper<NbTransferBatchEntity>();
        wrapper.selectAll(NbTransferBatchEntity.class)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getFirstName, NbDriverEntity::getLastName, NbDriverEntity::getMobile)
                .select(NbTransferCenterEntity::getTcId)
                .selectAs(NbSortingCenterEntity::getCenterName, NbTransferBatchPageVo.Fields.sortingCenterName)
                .selectAs(NbTransferCenterEntity::getCenterName, NbTransferBatchPageVo.Fields.transferCenterName)
                .selectAs(NbDriverEntity::getDriverName, NbTransferBatchPageVo.Fields.driverName);

        String addTime = vo.getCreateTime();// String createTime = "2024-03-25 00:00:00-2024-12-31 00:00:00";
        if (addTime != null) {
            int splitIndex = addTime.indexOf(":", addTime.indexOf(":") + 1) + 3;
            String startTime = addTime.substring(0, splitIndex);
            String endTime = addTime.substring(splitIndex + 1);
            wrapper.ge(NbTransferBatchEntity::getAddTime, startTime).le(NbTransferBatchEntity::getAddTime, endTime);    // 条件查询-创建时间
        }
        wrapper.eq(ObjectUtil.isNotNull(vo.getDriverId()), NbTransferBatchEntity::getDriverId, vo.getDriverId())        //条件查询-司机
                .eq(ObjectUtil.isNotNull(vo.getTcId()), NbTransferBatchEntity::getTcId, vo.getTcId())                   // 条件查询-转运中心
                .like(StrUtil.isNotBlank(vo.getBatchCode()), NbTransferBatchEntity::getBatchCode, vo.getBatchCode())    // 条件查询-路区码
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbTransferBatchEntity::getDriverId)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbTransferBatchEntity::getOwnerScId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbTransferBatchEntity::getTcId)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbTransferBatchEntity::getBatchId, ids)
                .orderByDesc(NbTransferBatchEntity::getAddTime);
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbTransferBatchEntity::getOwnerScId, idList);
        return wrapper;
    }
}