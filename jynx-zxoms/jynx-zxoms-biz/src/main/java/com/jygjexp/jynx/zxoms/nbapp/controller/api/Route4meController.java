package com.jygjexp.jynx.zxoms.nbapp.controller.api;

import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.send.service.NbRoute4meActivityService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: chenchang
 * @Description: APP-Route4Me接口
 * @Date: 2024/11/6 14:51
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/route4me" )
@Tag(description = "approute4me" , name = "APP-Route4Me开放接口" )
public class Route4meController {
    private final NbRoute4meActivityService nbRoute4meActivityService;

    @Operation(summary = "APP-index" , description = "APP-index" )
    @GetMapping("/index" )
    @Inner(value = false)
    public OldResult index(){
        return nbRoute4meActivityService.getIndex();
    }

    @Operation(summary = "APP-fixed" , description = "APP-fixed" )
    @GetMapping("/fixed" )
    @Inner(value = false)
    public OldResult fixed() {
        return nbRoute4meActivityService.getFixed();
    }

}
