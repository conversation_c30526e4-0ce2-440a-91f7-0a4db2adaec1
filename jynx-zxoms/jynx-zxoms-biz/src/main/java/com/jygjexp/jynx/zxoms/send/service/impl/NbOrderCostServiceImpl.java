package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderCostEntity;
import com.jygjexp.jynx.zxoms.entity.NbSortingCenterEntity;
import com.jygjexp.jynx.zxoms.entity.NbTransferCenterEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderCostMapper;
import com.jygjexp.jynx.zxoms.send.service.NbOrderCostService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.vo.OrderCostVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单费用
 *
 * <AUTHOR>
 * @date 2024-09-30 23:38:39
 */
@Service
@RequiredArgsConstructor
public class NbOrderCostServiceImpl extends ServiceImpl<NbOrderCostMapper, NbOrderCostEntity> implements NbOrderCostService {
    private final NbOrderCostMapper nbOrderCostMapper;
    private final NbSortingCenterService nbSortingCenterService;

    /**
     * 订单费用分页查询
     * @param page
     * @param basicNbOrderCost
     * @return
     */
    @Override
    public Page<OrderCostVo> search(Page page, NbOrderCostEntity basicNbOrderCost) {
        //select sc_id ID, center_name CN from nb_sorting_center; ds=nbd;
        //select tc_id ID, center_name Name, transfer_center_code Code from nb_transfer_center order by tc_id desc; ds=nbd;
        MPJLambdaWrapper<NbOrderCostEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbOrderCostEntity.class)
                .select(NbSortingCenterEntity::getScId)
                .selectAs(NbSortingCenterEntity::getCenterName, OrderCostVo.Fields.sortingCenterName)
                .select(NbTransferCenterEntity::getTcId)
                .selectAs(NbTransferCenterEntity::getTransferCenterCode, OrderCostVo.Fields.transferCenterCode)
                .selectAs(NbTransferCenterEntity::getCenterName, OrderCostVo.Fields.transferCenterName)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbOrderCostEntity::getTcId)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbOrderCostEntity::getScId)
//                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderCostEntity::getOrderId)
                .orderByDesc(NbOrderCostEntity::getOrderId);
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbOrderCostEntity::getScId, idList);
        return nbOrderCostMapper.selectJoinPage(page, OrderCostVo.class, wrapper);
    }

}