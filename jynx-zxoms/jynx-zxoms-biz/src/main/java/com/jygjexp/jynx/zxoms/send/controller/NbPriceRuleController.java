package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbPriceRuleEntity;
import com.jygjexp.jynx.zxoms.send.service.NbPriceRuleService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 报价表
 *
 * <AUTHOR>
 * @date 2024-11-08 10:35:26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbPriceRule" )
//@Tag(description = "nbPriceRule" , name = "报价表管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbPriceRuleController {

    private final  NbPriceRuleService nbPriceRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbPriceRule 报价表
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceRule_view')" )
    public R getNbPriceRulePage(@ParameterObject Page page, @ParameterObject NbPriceRuleEntity nbPriceRule) {
        return R.ok(nbPriceRuleService.search(page, nbPriceRule));
    }


    /**
     * 通过id查询报价表
     * @param ruleId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{ruleId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceRule_view')" )
    public R getById(@PathVariable("ruleId" ) Integer ruleId) {
        return R.ok(nbPriceRuleService.getById(ruleId));
    }

    /**
     * 新增报价表
     * @param nbPriceRule 报价表
     * @return R
     */
//    @Operation(summary = "新增报价表" , description = "新增报价表" )
//    @SysLog("新增报价表" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceRule_add')" )
    public R save(@RequestBody NbPriceRuleEntity nbPriceRule) {
        return R.ok(nbPriceRuleService.save(nbPriceRule));
    }

    /**
     * 修改报价表
     * @param nbPriceRule 报价表
     * @return R
     */
//    @Operation(summary = "修改报价表" , description = "修改报价表" )
//    @SysLog("修改报价表" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceRule_edit')" )
    public R updateById(@RequestBody NbPriceRuleEntity nbPriceRule) {
        return R.ok(nbPriceRuleService.updateById(nbPriceRule));
    }

    /**
     * 通过id删除报价表
     * @param ids ruleId列表
     * @return R
     */
//    @Operation(summary = "通过id删除报价表" , description = "通过id删除报价表" )
//    @SysLog("通过id删除报价表" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceRule_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbPriceRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbPriceRule 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceRule_export')" )
    public List<NbPriceRuleEntity> export(NbPriceRuleEntity nbPriceRule,Integer[] ids) {
        return nbPriceRuleService.list(Wrappers.lambdaQuery(nbPriceRule).in(ArrayUtil.isNotEmpty(ids), NbPriceRuleEntity::getRuleId, ids));
    }
}