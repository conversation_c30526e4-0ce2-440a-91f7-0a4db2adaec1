package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbDriverIncomeEntity;
import com.jygjexp.jynx.zxoms.send.service.NbDriverIncomeService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 司机收入
 *
 * <AUTHOR>
 * @date 2024-11-11 22:17:03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbDriverIncome" )
//@Tag(description = "nbDriverIncome" , name = "司机收入管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbDriverIncomeController {

    private final  NbDriverIncomeService nbDriverIncomeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbDriverIncome 司机收入
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverIncome_view')" )
    public R getNbDriverIncomePage(@ParameterObject Page page, @ParameterObject NbDriverIncomeEntity nbDriverIncome) {
        LambdaQueryWrapper<NbDriverIncomeEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbDriverIncomeService.page(page, wrapper));
    }


    /**
     * 通过id查询司机收入
     * @param incomeId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{incomeId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverIncome_view')" )
    public R getById(@PathVariable("incomeId" ) Integer incomeId) {
        return R.ok(nbDriverIncomeService.getById(incomeId));
    }

    /**
     * 新增司机收入
     * @param nbDriverIncome 司机收入
     * @return R
     */
//    @Operation(summary = "新增司机收入" , description = "新增司机收入" )
//    @SysLog("新增司机收入" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverIncome_add')" )
    public R save(@RequestBody NbDriverIncomeEntity nbDriverIncome) {
        return R.ok(nbDriverIncomeService.save(nbDriverIncome));
    }

    /**
     * 修改司机收入
     * @param nbDriverIncome 司机收入
     * @return R
     */
//    @Operation(summary = "修改司机收入" , description = "修改司机收入" )
//    @SysLog("修改司机收入" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverIncome_edit')" )
    public R updateById(@RequestBody NbDriverIncomeEntity nbDriverIncome) {
        return R.ok(nbDriverIncomeService.updateById(nbDriverIncome));
    }

    /**
     * 通过id删除司机收入
     * @param ids incomeId列表
     * @return R
     */
//    @Operation(summary = "通过id删除司机收入" , description = "通过id删除司机收入" )
//    @SysLog("通过id删除司机收入" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverIncome_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbDriverIncomeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbDriverIncome 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverIncome_export')" )
    public List<NbDriverIncomeEntity> export(NbDriverIncomeEntity nbDriverIncome,Integer[] ids) {
        return nbDriverIncomeService.list(Wrappers.lambdaQuery(nbDriverIncome).in(ArrayUtil.isNotEmpty(ids), NbDriverIncomeEntity::getIncomeId, ids));
    }
}