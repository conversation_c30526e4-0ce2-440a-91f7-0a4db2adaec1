package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbWeightDetailEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbWeightDetailMapper;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbWeightDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 重量段-明细
 *
 * <AUTHOR>
 * @date 2025-01-09 13:56:36
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NbWeightDetailServiceImpl extends ServiceImpl<NbWeightDetailMapper, NbWeightDetailEntity> implements NbWeightDetailService {

    private final NbWeightDetailMapper nbWeightDetailMapper;

    //导入重量明细
    @Override
    public R processFile(MultipartFile file, Long groupId) {
        // 验证文件类型和空文件
        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
            return LocalizedR.failed("order.upload.valid.file", Optional.ofNullable(null));
        }
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            //List<List<Object>> lines = reader.read(1, reader.getRowCount());
            List<List<Object>> lines = reader.read(1, reader.getRowCount()); // 跳过表头，确保从第二行开始读取
            // 验证Excel数据是否为空
            if (lines == null || lines.isEmpty()) {
                return LocalizedR.failed("order.upload.empty.data", Optional.ofNullable(null));
            }

            // 创建集合来记录所有问题的字段
            List<String> errorMessages = new ArrayList<>();
            List<NbWeightDetailEntity> weightList = new ArrayList<>();
            for (int i = 0; i < lines.size(); i++) {
                List<Object> line = lines.get(i);
                log.info("Processing line {}: {}", i, line);

                // 记录每行错误集合
                List<String> lineErrors = new ArrayList<>();

                // 验证字段
                BigDecimal pieceWeight = validateBigDecimalField(line, 0, "billing weight cannot be empty/计费单重不能为空", i + 1, lineErrors);
                BigDecimal startWeight = validateBigDecimalField(line, 1, "starting weight cannot be empty/开始重量不能为空", i + 1, lineErrors);
                BigDecimal endWeight = validateBigDecimalField(line, 2, "cut-off weight cannot be empty/截止重量不能为空", i + 1, lineErrors);
                Integer divisionId = validateIntegerField(line, 3, "weight partition cannot be empty/重量分区不能为空", i + 1, lineErrors);

                validateWeightField(line, 1, 2,groupId, "Weight interval repetition/重量区间重复", i + 1, lineErrors);

                if (startWeight.compareTo(endWeight)>=0){
                    lineErrors.add("row" + i+1 + "：" + "The cut-off weight cannot be less than the starting weight");
                }

                // 如果当前行有错误，跳过该行
                if (!lineErrors.isEmpty()) {
                    // 将当前行的所有错误添加到全局错误信息列表中
                    errorMessages.addAll(lineErrors);
                } else {
                    //准备插入字段阶段--------------------------------------
                    NbWeightDetailEntity weightDetail = new NbWeightDetailEntity();
                    weightDetail.setPieceWeight(pieceWeight);
                    weightDetail.setStartWeight(startWeight);
                    weightDetail.setEndWeight(endWeight);
                    weightDetail.setDivisionId(Long.valueOf(divisionId));
                    weightDetail.setGroupId(groupId);

                    weightList.add(weightDetail);
                }

            }

            // 保存新增至数据库
            Integer weightCount = processWeights(weightList);
            //记录成功条数信息
            String successMessage = "Successfully processed: " + weightCount + " rows.";

            if (errorMessages.isEmpty()) {
                log.info("重量明细Excel文件处理成功，共处理了 {0} 条", weightCount);
                return LocalizedR.ok("weight.delivery.note.Excel.file.processing.success", successMessage);
            } else {
                // 返回成功数量和错误信息
                return LocalizedR.failed("weight.file.processing.errors", successMessage + "<br>Errors:<br>" + errorMessages);
            }
        } catch (IOException e) {
            log.error("重量明细Excel文件处理异常", e);
            return LocalizedR.failed("weight.delivery.note.Excel.file.processing.exception", e.getMessage());
        }
    }


    // 校验文件是否是.xls或.xlsx格式
    private boolean isExcelFile(String fileName) {
        return fileName != null && (fileName.endsWith(".xls") || fileName.endsWith(".xlsx"));
    }

    //校验Integer类型字段是否为空
    private Integer validateIntegerField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            lineErrors.add("row" + row + "：" + value + "(format error)");
            return null;
        }
    }

    //校验Decimal类型字段是否为空
    private BigDecimal validateBigDecimalField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null || value.toString().trim().isEmpty()) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            lineErrors.add("row" + row + "：" + value + "(format error)");
            return null;
        }
    }

    //校验重量区间是否重复
    private String validateWeightField(List<Object> line, int index, int index2,Long groupId, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        Object value2 = line.get(index2);

        List<NbWeightDetailEntity> weightEntities = nbWeightDetailMapper.selectList(new LambdaQueryWrapper<NbWeightDetailEntity>()
                .eq(NbWeightDetailEntity::getStartWeight, value)
                .eq(NbWeightDetailEntity::getEndWeight, value2)
                .eq(NbWeightDetailEntity::getGroupId,groupId).last("limit 1"));
        if (CollUtil.isNotEmpty(weightEntities)) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        return value.toString().trim();
    }

    //处理批量导入重量明细
    public Integer processWeights(List<NbWeightDetailEntity> weightList) {
        Integer count = 0;
        for (NbWeightDetailEntity weight : weightList) {
            boolean save = this.save(weight);
            if (save) {
                count++;
            }
        }
        return count;
    }


}