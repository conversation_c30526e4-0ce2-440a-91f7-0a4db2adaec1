package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbRoute4meActivityEntity;
import com.jygjexp.jynx.zxoms.send.service.NbRoute4meActivityService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * route4me推送日志
 *
 * <AUTHOR>
 * @date 2024-10-22 15:40:52
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbRoute4meActivity" )
@Tag(description = "nbRoute4meActivity" , name = "route4me推送日志管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbRoute4meActivityController {

    private final  NbRoute4meActivityService nbRoute4meActivityService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbRoute4meActivity route4me推送日志
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbRoute4meActivity_view')" )
    public R getNbRoute4meActivityPage(@ParameterObject Page page, @ParameterObject NbRoute4meActivityEntity nbRoute4meActivity) {
        LambdaQueryWrapper<NbRoute4meActivityEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbRoute4meActivityService.page(page, wrapper));
    }


    /**
     * 通过id查询route4me推送日志
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbRoute4meActivity_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(nbRoute4meActivityService.getById(id));
    }

    /**
     * 新增route4me推送日志
     * @param nbRoute4meActivity route4me推送日志
     * @return R
     */
    @Operation(summary = "新增route4me推送日志" , description = "新增route4me推送日志" )
    @SysLog("新增route4me推送日志" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbRoute4meActivity_add')" )
    public R save(@RequestBody NbRoute4meActivityEntity nbRoute4meActivity) {
        return R.ok(nbRoute4meActivityService.save(nbRoute4meActivity));
    }

    /**
     * 修改route4me推送日志
     * @param nbRoute4meActivity route4me推送日志
     * @return R
     */
    @Operation(summary = "修改route4me推送日志" , description = "修改route4me推送日志" )
    @SysLog("修改route4me推送日志" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbRoute4meActivity_edit')" )
    public R updateById(@RequestBody NbRoute4meActivityEntity nbRoute4meActivity) {
        return R.ok(nbRoute4meActivityService.updateById(nbRoute4meActivity));
    }

    /**
     * 通过id删除route4me推送日志
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除route4me推送日志" , description = "通过id删除route4me推送日志" )
    @SysLog("通过id删除route4me推送日志" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbRoute4meActivity_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbRoute4meActivityService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbRoute4meActivity 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbRoute4meActivity_export')" )
    public List<NbRoute4meActivityEntity> export(NbRoute4meActivityEntity nbRoute4meActivity,Integer[] ids) {
        return nbRoute4meActivityService.list(Wrappers.lambdaQuery(nbRoute4meActivity).in(ArrayUtil.isNotEmpty(ids), NbRoute4meActivityEntity::getId, ids));
    }
}