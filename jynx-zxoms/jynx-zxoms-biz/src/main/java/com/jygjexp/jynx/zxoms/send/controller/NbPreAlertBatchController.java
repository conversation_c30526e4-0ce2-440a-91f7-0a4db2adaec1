package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbPreAlertBatchEntity;
import com.jygjexp.jynx.zxoms.send.service.NbPreAlertBatchService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * PreAlertBatch
 *
 * <AUTHOR>
 * @date 2024-11-07 15:55:13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbPreAlertBatch" )
//@Tag(description = "nbPreAlertBatch" , name = "PreAlertBatch管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbPreAlertBatchController {

    private final  NbPreAlertBatchService nbPreAlertBatchService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbPreAlertBatch PreAlertBatch
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPreAlertBatch_view')" )
    public R getNbPreAlertBatchPage(@ParameterObject Page page, @ParameterObject NbPreAlertBatchEntity nbPreAlertBatch) {
        return R.ok(nbPreAlertBatchService.search(page, nbPreAlertBatch));
    }


    /**
     * 通过id查询PreAlertBatch
     * @param id id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPreAlertBatch_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(nbPreAlertBatchService.getById(id));
    }

    /**
     * 新增PreAlertBatch
     * @param nbPreAlertBatch PreAlertBatch
     * @return R
     */
//    @Operation(summary = "新增PreAlertBatch" , description = "新增PreAlertBatch" )
//    @SysLog("新增PreAlertBatch" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPreAlertBatch_add')" )
    public R save(@RequestBody NbPreAlertBatchEntity nbPreAlertBatch) {
        return R.ok(nbPreAlertBatchService.save(nbPreAlertBatch));
    }

    /**
     * 修改PreAlertBatch
     * @param nbPreAlertBatch PreAlertBatch
     * @return R
     */
//    @Operation(summary = "修改PreAlertBatch" , description = "修改PreAlertBatch" )
//    @SysLog("修改PreAlertBatch" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPreAlertBatch_edit')" )
    public R updateById(@RequestBody NbPreAlertBatchEntity nbPreAlertBatch) {
        return R.ok(nbPreAlertBatchService.updateById(nbPreAlertBatch));
    }

    /**
     * 通过id删除PreAlertBatch
     * @param ids id列表
     * @return R
     */
//    @Operation(summary = "通过id删除PreAlertBatch" , description = "通过id删除PreAlertBatch" )
//    @SysLog("通过id删除PreAlertBatch" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPreAlertBatch_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbPreAlertBatchService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbPreAlertBatch 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbPreAlertBatch_export')" )
    public List<NbPreAlertBatchEntity> export(NbPreAlertBatchEntity nbPreAlertBatch,Integer[] ids) {
        return nbPreAlertBatchService.list(Wrappers.lambdaQuery(nbPreAlertBatch).in(ArrayUtil.isNotEmpty(ids), NbPreAlertBatchEntity::getId, ids));
    }
}