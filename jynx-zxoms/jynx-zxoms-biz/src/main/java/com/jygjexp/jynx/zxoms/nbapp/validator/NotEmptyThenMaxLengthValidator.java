package com.jygjexp.jynx.zxoms.nbapp.validator;

import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.NotEmptyThenMaxLength;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/7 17:54
 */
public class NotEmptyThenMaxLengthValidator implements ConstraintValidator<NotEmptyThenMaxLength, String> {

    private int maxLength;

    @Override
    public void initialize(NotEmptyThenMaxLength constraintAnnotation) {
        maxLength = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果值不为 null 或空字符串，检查最大长度
        return value == null || value.isEmpty() || value.length() <= maxLength;
    }
}
