package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.service.JynxUser;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.dto.DriverDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.mapper.NbDriverMapper;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderPathMapper;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbDriverService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.service.NbTransferCenterService;
import com.jygjexp.jynx.zxoms.send.service.NbVehicleTypeService;
import com.jygjexp.jynx.zxoms.send.utils.AliYunOSS;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;
import com.jygjexp.jynx.zxoms.vo.DriverVo;
import com.jygjexp.jynx.zxoms.vo.NbDriverExcelVo;
import com.jygjexp.jynx.zxoms.vo.NbDriverPageVo;
import com.jygjexp.jynx.zxoms.vo.PaisongDriverPageVo;
import com.route4me.sdk.services.users.User;
import com.route4me.sdk.services.vehicles.Vehicles;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * 司机
 *
 * <AUTHOR>
 * @date 2024-10-15 22:42:15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NbDriverServiceImpl extends ServiceImpl<NbDriverMapper, NbDriverEntity> implements NbDriverService {
    private final NbDriverMapper nbDriverMapper;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbTransferCenterService nbTransferCenterService;
    private final NbVehicleTypeService nbVehicleTypeService;
    private final RemoteUserService remoteUserService;
    private final Route4MeUtil route4MeUtil;
    private final NbOrderPathMapper nbOrderPathMapper;
    private final RedisTemplate<String, String> redisTemplate;

    /**
     * 司机信息分页查询
     * @param page
     * @param entity
     * @return
     */
    @Override
    public Page<NbDriverPageVo> search(Page page, NbDriverEntity entity) {
        MPJLambdaWrapper wrapper = getWrapper(entity, null);
        return nbDriverMapper.selectJoinPage(page, NbDriverPageVo.class, wrapper);
    }

    /**
     * 获取派送司机分页列表  必须是审核通过且业务类型为1、3（派送）的有效司机
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<PaisongDriverPageVo> getPaisongDriver(Page page, PaisongDriverPageVo vo) {
        //select * FROM nb_driver where business_type in (1,3) and audit_status =3
        MPJLambdaWrapper<NbDriverEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbDriverEntity.class)
                .selectAs(NbTransferCenterEntity::getCenterName, PaisongDriverPageVo.Fields.transferCenterName)
                .selectAs(NbSortingCenterEntity::getCenterName, PaisongDriverPageVo.Fields.sortingCenterName)
                .eq(ObjectUtil.isNotNull(vo.getDriverId()), NbDriverEntity::getDriverId, vo.getDriverId())          // 条件查询-司机ID
                .like(StrUtil.isNotBlank(vo.getDriverName()), NbDriverEntity::getDriverName, vo.getDriverName())    // 条件查询-司机全名
                .like(StrUtil.isNotBlank(vo.getMobile()), NbDriverEntity::getMobile, vo.getMobile())                // 条件查询-手机号
                .like(StrUtil.isNotBlank(vo.getEmail()), NbDriverEntity::getEmail, vo.getEmail())                   // 条件查询-邮箱
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), NbDriverEntity::getIsValid, vo.getIsValid())    // 条件查询-是否启用
                .eq(ObjectUtil.isNotNull(vo.getRoute4meMemberId()), NbDriverEntity::getRoute4meMemberId, vo.getRoute4meMemberId())  // 条件查询-R4M MID
                .eq(ObjectUtil.isNotNull(vo.getTcId()), NbDriverEntity::getTcId, vo.getTcId())  // 条件查询-转运中心
                .eq(ObjectUtil.isNotNull(vo.getAutoRoutePlanning()), NbDriverEntity::getAutoRoutePlanning, vo.getAutoRoutePlanning())   //条件查询-自动路径规划
                .in(NbDriverEntity::getBusinessType, 1, 3)
                .eq(NbDriverEntity::getAuditStatus, 3)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbDriverEntity::getTcId)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbDriverEntity::getScId)
                .orderByDesc(NbDriverEntity::getRegTime);
        return nbDriverMapper.selectJoinPage(page, PaisongDriverPageVo.class, wrapper);
    }

    @Override
    public R UpdatePassword(String mobile,String password) {
        NbDriverEntity appDriverList = nbDriverMapper.selectOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getMobile, mobile),false);
        appDriverList.setPassword(password);
        nbDriverMapper.updateById(appDriverList);
        return R.ok();
    }

    // 上下架司机列表分页查询-只查询派送司机，且为审核通过的
    @Override
    public Page<NbDriverEntity> pagePutawayOrPickupName(Page page, NbDriverEntity vo) {
        MPJLambdaWrapper<NbDriverEntity> wrapper = new MPJLambdaWrapper<NbDriverEntity>();
        wrapper.select(NbDriverEntity::getDriverId, NbDriverEntity::getDriverName, NbDriverEntity::getFirstName)     // 显示司机名称-老外只喜欢用名
                .like(StrUtil.isNotBlank(vo.getDriverName()), NbDriverEntity::getDriverName, vo.getDriverName())    // 条件查询-司机名称
                .eq(NbDriverEntity::getBusinessType, 1)     // 只筛选派送司机
                .eq(NbDriverEntity::getAuditStatus, 3)      // 只筛选审核通过的司机
                .orderByDesc(NbDriverEntity::getDriverId);
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbDriverEntity::getScId, idList);    // 筛选出当前用户所属的分拣中心
        return nbDriverMapper.selectPage(page, wrapper);
    }

    /**
     * 查询所有上级司机leader
     * 多个地方有用到
     * @param page
     * @return
     */
    @Override
    public Page<NbDriverPageVo> pageListPDriverId(Page page, NbDriverEntity entity) {
        // select driver_id, first_name, last_name, mobile from nb_driver where is_leader = true; ds=nbd;
        MPJLambdaWrapper<NbDriverEntity> wrapper = new MPJLambdaWrapper<NbDriverEntity>();
        wrapper.select(NbDriverEntity::getDriverId ,NbDriverEntity::getFirstName, NbDriverEntity::getLastName, NbDriverEntity::getMobile)
                .select(NbDriverEntity::getPDriverId)
                .selectAs(NbDriverEntity::getDriverName, NbDriverPageVo.Fields.pDriverName)
                .eq(NbDriverEntity::getIsLeader, true);
        // 如果 PDriverId 不为 null 或 0，则加入此条件
        if (ObjectUtil.isNotNull(entity.getPDriverId()) && entity.getPDriverId() > 0) {
            wrapper.eq(NbDriverEntity::getPDriverId, entity.getPDriverId());
        }
        return nbDriverMapper.selectJoinPage(page, NbDriverPageVo.class, wrapper);
    }

    /**
     * 审核通过
     * @param drivers
     * @return
     */
    @Override
    public R auditPass(NbDriverEntity drivers) {
//        Integer driverId = getSelectValueToInt("driver_id");
//		Boolean isPass = getBoolean("isPass");

        NbDriverEntity driver = getById(drivers.getDriverId());
        // 业务类型为1，或者3 派送的司机 且 状态为审核通过的司机才可以创建route4Me用户
        if ((drivers.getBusinessType() == 1 || drivers.getBusinessType() == 3)) {
            if (driver.getRoute4meMemberId() == null || driver.getRoute4meMemberId() == 0) {
                try {
                    R r = route4MeUtil.createUser(driver);
                    if (r.isOk()) {
                        User r4mUser = (User) r.getData();
                        driver.setRoute4meMemberId(Integer.valueOf(r4mUser.getMemberId()));
                        nbDriverMapper.updateById(driver);
                    } else {
                        String rawErrorMsg = r.getMsg();

                        // 提取 errors 内容
                        StringBuilder parsedErrors = new StringBuilder();
                        if (rawErrorMsg != null && rawErrorMsg.contains("{")) {
                            try {
                                String jsonPart = rawErrorMsg.substring(rawErrorMsg.indexOf("{"));
                                cn.hutool.json.JSONObject jsonObject = cn.hutool.json.JSONUtil.parseObj(jsonPart);

                                if (jsonObject.containsKey("errors")) {
                                    Object errorsObj = jsonObject.get("errors");

                                    if (errorsObj instanceof JSONArray) {
                                        JSONArray errorsArray = (JSONArray) errorsObj;
                                        for (int i = 0; i < errorsArray.size(); i++) {
                                            parsedErrors.append(errorsArray.getStr(i));
                                            if (i < errorsArray.size() - 1) {
                                                parsedErrors.append("; ");
                                            }
                                        }
                                    } else if (errorsObj instanceof JSONObject) {
                                        JSONObject errorsDict = (JSONObject) errorsObj;
                                        for (String key : errorsDict.keySet()) {
                                            parsedErrors.append(key);
                                            break; // 只取第一个
                                        }
                                    } else if (errorsObj instanceof String) {
                                        parsedErrors.append((String) errorsObj);
                                    }

                                }
                            } catch (Exception ex) {
                                log.warn("JSON parsing failed, using raw error message", ex);
                            }
                        }

                        String finalErrorMsg = parsedErrors.length() > 0 ? parsedErrors.toString() : rawErrorMsg;
                        return LocalizedR.failed("nbdriver.created.failed", finalErrorMsg);
                    }
                } catch (RuntimeException e) {
                    log.error("Route4Me New driver error", e);
                    throw new HttpClientErrorException(HttpStatus.BAD_REQUEST);
                }
            } else {
                route4MeUtil.updateUser(driver);

            }

            NbTransferCenterEntity tc = nbTransferCenterService.getById(drivers.getTcId());
            Integer scId = tc.getScId();
            NbSortingCenterEntity sc = nbSortingCenterService.getById(scId);


            if (StringUtils.isBlank(driver.getRoute4meVehicleId())) {
                Vehicles vehicle = new Vehicles();
                vehicle.setMemberId(driver.getRoute4meMemberId().toString());
                vehicle.setVehicleAlias(sc.getScCode() + "-" + tc.getTransferCenterCode() + "-" + driver.getDriverId() + "-" + driver.getFirstName() + " " + driver.getLastName());
//				vehicle.setVehicleVin(driver.getv);
                vehicle.setVehicleLicensePlate(driver.getPlateNumber());

                if (driver.getVehicleTypeId() != null) {
                    NbVehicleTypeEntity vt = nbVehicleTypeService.getById(driver.getVehicleTypeId());
                    vehicle.setVehicleTypeId(vt.getRoute4meKey());
                }
                vehicle.setVehicleMake(driver.getR4mVehicleMake());
                if (driver.getR4mFuelType() != null) {
                    vehicle.setFuelType(driver.getR4mFuelType().toLowerCase());
                }
                if (StringUtils.isNotBlank(driver.getR4mVehicleCapacityProfileId())) {
                    vehicle.setVehicleCapacityProfileId(Integer.valueOf(driver.getR4mVehicleCapacityProfileId()));
                }
                try {
                    vehicle = route4MeUtil.createVehicle(vehicle);
                } catch (Exception e) {
                    log.error("Route4Me New vehicle error", e);
                    throw new HttpClientErrorException(HttpStatus.BAD_REQUEST);
                }
                driver.setRoute4meVehicleId(vehicle.getVehicleId());
                nbDriverMapper.updateById(driver);

            } else {
                Vehicles vehicle = new Vehicles();
                vehicle.setMemberId(driver.getRoute4meMemberId().toString());
                vehicle.setVehicleAlias(sc.getScCode() + "-" + tc.getTransferCenterCode() + "-" + driver.getDriverId() + "-" + driver.getFirstName() + " " + driver.getLastName());
//				vehicle.setVehicleVin(driver.getv);
                vehicle.setVehicleLicensePlate(driver.getPlateNumber());

                if (driver.getVehicleTypeId() != null) {
                    NbVehicleTypeEntity vt = nbVehicleTypeService.getById(driver.getVehicleTypeId());
                    vehicle.setVehicleTypeId(vt.getRoute4meKey());
                }
                vehicle.setVehicleMake(driver.getR4mVehicleMake());
                vehicle.setFuelType(driver.getR4mFuelType());
                if (StringUtils.isNotBlank(driver.getR4mVehicleCapacityProfileId())) {
                    vehicle.setVehicleCapacityProfileId(Integer.valueOf(driver.getR4mVehicleCapacityProfileId()));
                }

                route4MeUtil.updateVehicle(vehicle);
            }
            driver.setAuditStatus(DriverDto.AUDIT_STATUS_3_PASS);
            driver.setAuditTime(new Date());
            driver.setAutoRoutePlanning(drivers.getAutoRoutePlanning());
            driver.setIsLeader(drivers.getIsLeader());
            if (null != drivers.getBusinessType() && drivers.getBusinessType() != 0){
                driver.setBusinessType(drivers.getBusinessType());
            }
            if (null != drivers.getWarehouseId() && drivers.getWarehouseId() != 0){
                driver.setWarehouseId(drivers.getWarehouseId());
            }

            if (null != drivers.getTcId() && drivers.getTcId() != 0){
                driver.setTcId(drivers.getTcId());
            }

            if (null !=drivers.getPDriverId() && drivers.getPDriverId() != 0){
                driver.setPDriverId(drivers.getPDriverId());
            }

            if (null != drivers.getWorkTypeId() && drivers.getWorkTypeId() != 0){
                driver.setWorkTypeId(drivers.getWorkTypeId());
            }
            nbDriverMapper.updateById(driver);
            return LocalizedR.ok("nbdriver.driver.audit.passed", Optional.ofNullable(null));
        } else if (drivers.getBusinessType() == 2) {
            // 退件和 派送或退件的司机 直接审核通过即可
            driver.setAuditStatus(DriverDto.AUDIT_STATUS_3_PASS);
            driver.setAuditTime(new Date());
            driver.setAutoRoutePlanning(drivers.getAutoRoutePlanning());
            driver.setIsLeader(drivers.getIsLeader());

            if (null != drivers.getBusinessType() && drivers.getBusinessType() != 0){
                driver.setBusinessType(drivers.getBusinessType());
            }
            if (null != drivers.getWarehouseId() && drivers.getWarehouseId() != 0){
                driver.setWarehouseId(drivers.getWarehouseId());
            }

//            if (null != drivers.getTcId() && drivers.getTcId() != 0){
//                driver.setTcId(drivers.getTcId());
//            }

            if (null !=drivers.getPDriverId() && drivers.getPDriverId() != 0){
                driver.setPDriverId(drivers.getPDriverId());
            }

            if (null != drivers.getWorkTypeId() && drivers.getWorkTypeId() != 0){
                driver.setWorkTypeId(drivers.getWorkTypeId());
            }
            nbDriverMapper.updateById(driver);
        } else {
            return LocalizedR.failed("nbdriver.business.type.is.incorrect", Optional.ofNullable(null));
        }
        return LocalizedR.ok("nbdriver.audit.passed", Optional.ofNullable(null));
    }

    /**
     * 审核拒绝
     * @param driverId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R auditRefuse(Integer driverId) {
//		Boolean isPass = getBoolean("isPass");

        NbDriverEntity driver = getById(driverId);
        if (driver.getAuditStatus() == 1 || driver.getAuditStatus() == 2) {

            driver.setRegStep("000000");
            driver.setAuditStatus(4);
            driver.setAuditTime(new Date());
            nbDriverMapper.updateById(driver);

            return R.ok();
        } else {
            return LocalizedR.failed("nbdriver.status.inoperable", Optional.ofNullable(null));
        }
    }

    /**
     * 审核驳回
     * @param driverVo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R auditRefuseReason(DriverVo driverVo) {
        NbDriverEntity driver = getById(driverVo.getDriverId());
        if (driver.getAuditStatus() == DriverDto.AUDIT_STATUS_3_PASS) {
            return LocalizedR.failed("nbdriver.already.approved.for.review", Optional.ofNullable(null));
        }

        if (driver.getAuditStatus() == DriverDto.AUDIT_STATUS_4_REFUSE) {
            return LocalizedR.failed("nbdriver.repeat.operation", Optional.ofNullable(null));
        }

//        String regStep = driver.getRegStep();
//        JSONArray ja = JSON.parseArray(driverVo.getItem());
//        for (int i=0; i<ja.size(); i++) {
//            JSONObject item1 = ja.getJSONObject(i);
//            int id = item1.getInteger("value");
//            if (id == 1) {
//                regStep = setChatAt(regStep, 1, '0');
//            } else if (id == 2) {
//                regStep = setChatAt(regStep, 2, '0');
//            } else if (id == 3) {
//                regStep = setChatAt(regStep, 3, '0');
//            } else if (id == 4) {
//                regStep = setChatAt(regStep, 4, '0');
//            }
//        }
//        regStep = setChatAt(regStep, 5, '0');
        driver.setRefuseReason(driverVo.getReason());
//        driver.setRegStep(regStep);
        driver.setAuditStatus(DriverDto.AUDIT_STATUS_4_REFUSE);
        driver.setAuditTime(new Date());
        nbDriverMapper.updateById(driver);

        return LocalizedR.ok("nbdriver.msg.rejected", Optional.ofNullable(null));
    }

    public static String setChatAt(String str, int index, char tarStr) {
        if (str.length() < index + 1) {
            throw new RuntimeException("长度溢出");
        }
        StringBuffer sb = new StringBuffer(str.length());
        for (int i=0; i<str.length(); i++) {
            if (i == index) {
                sb.append(tarStr);
            } else {
                sb.append(str.charAt(i));
            }
        }
        return sb.toString();
    }

    /**
     * 司机置无效
     */
    public R driverInvalid(Integer driverId) {
        NbDriverEntity driver = getById(driverId);

        if (driver.getIsValid() == false) {
            return LocalizedR.failed("nbdriver.invalid.state", Optional.ofNullable(null));
        }
        JynxUser user = SecurityUtils.getUser();
        log.info("司机已被管理员置为无效:adminId=" + user.getId() + ",driverId=" + driverId);

        // 司机所在的派送包裹是在途、派送失败的包裹，才可以置为无效    203，204,280,286,290
        //SELECT op.order_status FROM `nb_driver` d LEFT JOIN nb_order_path op on op.driver_id = d.driver_id
        //WHERE op.order_status in(203,204,280,286,290) and d.driver_id = '4'
        MPJLambdaWrapper<NbOrderPathEntity> wrapper = new MPJLambdaWrapper<NbOrderPathEntity>();
        wrapper.select(NbOrderPathEntity::getOrderStatus)
                .in(NbOrderPathEntity::getOrderStatus, Arrays.asList(203, 204, 280, 286, 290))
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbOrderPathEntity::getDriverId)
                .eq(NbDriverEntity::getDriverId, driverId);
        List<NbOrderPathEntity> pathEntityList = nbOrderPathMapper.selectJoinList(NbOrderPathEntity.class, wrapper);
        if (CollUtil.isNotEmpty(pathEntityList)){
            log.info("司机所在的派送包裹是在途、派送失败的包裹，才可以置为无效");
            return LocalizedR.failed("nbdriver.invalid.state.reason", Optional.ofNullable(null));
        }

        if (driver.getBusinessType() == 1 || driver.getBusinessType() == 3) {
            driver.setIsValid(false);
            if (driver.getRoute4meMemberId() != null && driver.getRoute4meMemberId() > 0) {
                route4MeUtil.removeUser(driver);
                driver.setRoute4meMemberId(0);
            }
            nbDriverMapper.updateById(driver);
        }else if ((driver.getBusinessType() == 2)){
            driver.setIsValid(false);
        }
        nbDriverMapper.updateById(driver);
        return R.ok();
    }

    /**
     * 司机设置有效
     */
    public R driverValid(Integer driverId) {
        NbDriverEntity driver = getById(driverId);

        if (driver.getIsValid() == true) {
            return LocalizedR.failed("nbdriver.valid.state", Optional.ofNullable(null));
        }
        JynxUser user = SecurityUtils.getUser();
        log.info("司机已被管理员置为有效:adminId=" + user.getId() + ",driverId=" + driverId);

        //开启前判断，是否有相同手机号的有效司机存在于系统中
        NbDriverEntity nbDriverEntity = nbDriverMapper.selectOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getMobile, driver.getMobile())
                .ne(NbDriverEntity::getDriverId, driverId).eq(NbDriverEntity::getIsValid, true));
        if (nbDriverEntity != null){
            return LocalizedR.failed("nbdriver.mobile.is.already.occupied", driver.getMobile());
        }
        driver.setIsValid(true);
        if (driver.getEmail() != null && driver.getRoute4meMemberId() == 0) {
            R r = route4MeUtil.createUser(driver);
            if (r.isOk()) {
                User r4mUser = (User) r.getData();
                driver.setRoute4meMemberId(Integer.valueOf(r4mUser.getMemberId()));
            }
        }
        nbDriverMapper.updateById(driver);
        return R.ok();
    }

    /**
     * 设置邮箱
     */
    public R modifyEmail(Integer driverId, String email) {
        NbDriverEntity driver = getById(driverId);

        if (driver.getRoute4meMemberId() != null && driver.getRoute4meMemberId() > 0) {
            return LocalizedR.failed("nbdriver.already.synchronized.no.modify.email", Optional.ofNullable(null));
        }

        driver.setEmail(email);
        nbDriverMapper.updateById(driver);
        return R.ok();
    }

    /**
     * 更新车辆信息
     */
    public R updateVehicle(Integer driverId) {
        NbDriverEntity driver = getById(driverId);

        Integer scId = driver.getScId();
        NbSortingCenterEntity sc = nbSortingCenterService.getById(scId);
        NbTransferCenterEntity tc = nbTransferCenterService.getById(driver.getTcId());

        if (StringUtils.isBlank(driver.getRoute4meVehicleId())) {
            Vehicles vehicle = new Vehicles();
            vehicle.setMemberId(driver.getRoute4meMemberId().toString());
            vehicle.setVehicleAlias(sc.getScCode() + "-" + tc.getTransferCenterCode() + "-" + driver.getDriverId() + "-" + driver.getFirstName() + " " + driver.getLastName());
//			vehicle.setVehicleVin(driver.getv);
            vehicle.setVehicleLicensePlate(driver.getPlateNumber());

            if (driver.getVehicleTypeId() != null) {
                NbVehicleTypeEntity vt = nbVehicleTypeService.getById(driver.getVehicleTypeId());
                vehicle.setVehicleTypeId(vt.getRoute4meKey());
            }
            vehicle.setVehicleMake(driver.getR4mVehicleMake());
            if (driver.getR4mFuelType() != null) {
                vehicle.setFuelType(driver.getR4mFuelType().toLowerCase());
            }
            if (StringUtils.isNotBlank(driver.getR4mVehicleCapacityProfileId())) {
                vehicle.setVehicleCapacityProfileId(Integer.valueOf(driver.getR4mVehicleCapacityProfileId()));
            }
            vehicle = route4MeUtil.createVehicle(vehicle);

            driver.setRoute4meVehicleId(vehicle.getVehicleId());
            nbDriverMapper.updateById(driver);

        } else {
            Vehicles vehicle = new Vehicles();
            vehicle.setMemberId(driver.getRoute4meMemberId().toString());
            vehicle.setVehicleAlias(sc.getScCode() + "-" + tc.getTransferCenterCode() + "-" + driver.getDriverId() + "-" + driver.getFirstName() + " " + driver.getLastName());
//			vehicle.setVehicleVin(driver.getv);
            vehicle.setVehicleLicensePlate(driver.getPlateNumber());

            if (driver.getVehicleTypeId() != null) {
                NbVehicleTypeEntity vt = nbVehicleTypeService.getById(driver.getVehicleTypeId());
                vehicle.setVehicleTypeId(vt.getRoute4meKey());
            }
            vehicle.setVehicleMake(driver.getR4mVehicleMake());
            vehicle.setFuelType(driver.getR4mFuelType());
            if (StringUtils.isNotBlank(driver.getR4mVehicleCapacityProfileId())) {
                vehicle.setVehicleCapacityProfileId(Integer.valueOf(driver.getR4mVehicleCapacityProfileId()));
            }
            route4MeUtil.updateVehicle(vehicle);
        }
        return R.ok();
    }

    @Override
    public R checkAddDriverBefore(NbDriverEntity nbDriver) {
        Integer pDriverId = nbDriver.getPDriverId();
        String email = nbDriver.getEmail();
        if (StringUtils.isBlank(email)) {
            return LocalizedR.failed("nbdriver.email.no.empty", Optional.ofNullable(null));
        }
        email = email.trim();

        String mobile = nbDriver.getMobile();
        if (StringUtils.isBlank(mobile)) {
            return LocalizedR.failed("nbdriver.mobile.no.empty", Optional.ofNullable(null));
        }
        mobile = mobile.trim();

        if (pDriverId != null && pDriverId > 0) {
            NbDriverEntity driver = getById(pDriverId);
            if (driver == null) {
                return LocalizedR.failed("nbdriver.leader.does.not.have.id", pDriverId);
            }
            if (!driver.getIsLeader()) {
                return LocalizedR.failed("nbdriver.leader.is.not.the.leader", pDriverId);
            }
        }
        LambdaQueryWrapper<NbDriverEntity> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(NbDriverEntity::getEmail, email);
        NbDriverEntity existD = getOne(wrapper1,false);
        if (existD != null) {
            return LocalizedR.failed("nbdriver.email.is.already.occupied", email);
        }

        LambdaQueryWrapper<NbDriverEntity> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(NbDriverEntity::getMobile, mobile);
        existD = getOne(wrapper2,false);
        if (existD != null) {
            return LocalizedR.failed("nbdriver.mobile.is.already.occupied", mobile);
        }
        return LocalizedR.ok("nbdriver.success.under.review", Optional.ofNullable(null));
    }

    @Override
    public R processAddSucceed(NbDriverEntity nbDriver) {
        Integer driverId = nbDriver.getDriverId();
        NbDriverEntity driver = getById(driverId);

        // 创建成功后，走审核通过的逻辑       这里只有在业务类型为派送的司机才走这个逻辑
        if (driver.getBusinessType() == 1) {
            R r = route4MeUtil.createUser(driver);
            if (r.isOk()) {
                User user = (User) r.getData();
                driver.setRoute4meMemberId(Integer.valueOf(user.getMemberId()));
                nbDriverMapper.updateById(driver);
            } else {
                removeById(driverId);
                return LocalizedR.failed("nbdriver.error.synchronizing.to.route4me", r.getMsg());
            }
            Integer scId = driver.getScId();
            NbSortingCenterEntity sc = nbSortingCenterService.getById(scId);
            NbTransferCenterEntity tc = nbTransferCenterService.getById(driver.getTcId());

            if (StringUtils.isBlank(driver.getRoute4meVehicleId())) {
                Vehicles vehicle = new Vehicles();
                vehicle.setMemberId(driver.getRoute4meMemberId().toString());
                vehicle.setVehicleAlias(sc.getScCode() + "-" + tc.getTransferCenterCode() + "-" + driver.getDriverId() + "-" + driver.getFirstName() + " " + driver.getLastName());
//			vehicle.setVehicleVin(driver.getv);
                vehicle.setVehicleLicensePlate(driver.getPlateNumber());

                if (driver.getVehicleTypeId() != null) {
                    NbVehicleTypeEntity vt = nbVehicleTypeService.getById(driver.getVehicleTypeId());
                    vehicle.setVehicleTypeId(vt.getRoute4meKey());
                }
                vehicle.setVehicleMake(driver.getR4mVehicleMake());
                if (driver.getR4mFuelType() != null) {
                    vehicle.setFuelType(driver.getR4mFuelType().toLowerCase());
                }
                if (StringUtils.isNotBlank(driver.getR4mVehicleCapacityProfileId())) {
                    vehicle.setVehicleCapacityProfileId(Integer.valueOf(driver.getR4mVehicleCapacityProfileId()));
                }
                vehicle = route4MeUtil.createVehicle(vehicle);

                driver.setRoute4meVehicleId(vehicle.getVehicleId());
                nbDriverMapper.updateById(driver);

            } else {
                Vehicles vehicle = new Vehicles();
                vehicle.setMemberId(driver.getRoute4meMemberId().toString());
                vehicle.setVehicleAlias(sc.getScCode() + "-" + tc.getTransferCenterCode() + "-" + driver.getDriverId() + "-" + driver.getFirstName() + " " + driver.getLastName());
//			vehicle.setVehicleVin(driver.getv);
                vehicle.setVehicleLicensePlate(driver.getPlateNumber());

                if (driver.getVehicleTypeId() != null) {
                    NbVehicleTypeEntity vt = nbVehicleTypeService.getById(driver.getVehicleTypeId());
                    vehicle.setVehicleTypeId(vt.getRoute4meKey());
                }
                vehicle.setVehicleMake(driver.getR4mVehicleMake());
                vehicle.setFuelType(driver.getR4mFuelType());
                if (StringUtils.isNotBlank(driver.getR4mVehicleCapacityProfileId())) {
                    vehicle.setVehicleCapacityProfileId(Integer.valueOf(driver.getR4mVehicleCapacityProfileId()));
                }

                route4MeUtil.updateVehicle(vehicle);
            }
            // 审核通过
            driver.setAuditStatus(DriverDto.AUDIT_STATUS_3_PASS);
            driver.setAuditTime(new Date());
            nbDriverMapper.updateById(driver);
        } else if (driver.getBusinessType() == 2 || driver.getBusinessType() == 3) {
            // 设置为待审核
            driver.setAuditStatus(DriverDto.AUDIT_STATUS_2_SUBMIT);
            driver.setAuditTime(new Date());
            nbDriverMapper.updateById(driver);
        }
        return R.ok();
    }
    @Override
    public R checkDriverScId(){
        R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
        SysUser user = sysUserR.getData();
        String nbdScId = user.getNbdScId();
        if (StringUtils.isBlank(nbdScId)) {
            return R.ok();
        }

        String[] ids = nbdScId.split(",");
        List<Integer> idList = new ArrayList<>();
        for (String id : ids) {
            idList.add(Integer.valueOf(id));
        }

        LambdaQueryWrapper<NbDriverEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NbDriverEntity::getScId, idList)
                .or().eq(NbDriverEntity::getScId, 0);
        return R.ok(list(queryWrapper));
    }

    /**
     * 查询审核中的司机
     * @param driverId
     * @return
     */
    @Override
    public List<NbDriverEntity> findAuditDriver(Integer driverId) {
        LambdaQueryWrapper<NbDriverEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull(driverId), NbDriverEntity::getDriverId, driverId);
        wrapper.eq(NbDriverEntity::getAuditStatus, DriverDto.AUDIT_STATUS_2_SUBMIT);
        return list(wrapper);
    }

    /**
     * 通过sessionID查询登录司机
     * @param accessKey
     * @return
     */
    @Override
    public NbDriverEntity findBySessionId(String accessKey) {
        return getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getSessionId, accessKey));
    }

    /**
     * 校验新增或编辑司机手机号是否重复
     * @param mobile
     * @return
     */
    @Override
    public Boolean checkMobile(String mobile) {
        LambdaQueryWrapper<NbDriverEntity> wrapper = new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getMobile, mobile).last("limit 1");
        return getOne(wrapper) != null;
    }

    /**
     * 校验编辑司机手机号是否重复
     * @param mobile
     * @return
     */
    @Override
    public Boolean checkUpdateMobile(String mobile, Integer driverId) {
        LambdaQueryWrapper<NbDriverEntity> wrapper = new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getMobile, mobile).eq(NbDriverEntity::getIsValid, 1).last("limit 1");
        NbDriverEntity entity = getOne(wrapper);
        if (null != entity && entity.getDriverId().equals(driverId)) {
            return false;
        }
        return getOne(wrapper) != null;
    }

    @Override
    public R uploadFile(MultipartFile file, String dir, Long groupId, String type) {
        String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("bucketName", "nbexpress");
        resultMap.put("fileName", fileName);

        String fileUrl = null;
        try {
            // 调用 sendToOss 方法上传文件到 OSS 并获取文件 URL
            fileUrl = AliYunOSS.sendToOssTwo(file,dir,fileName);
            // 设置返回结果的 URL
            resultMap.put("url", fileUrl);
        } catch (Exception e) {
            log.error("上传失败", e);
            return R.failed(e.getLocalizedMessage());
        }
        return R.ok(resultMap);
    }

    // 司机列表条件构造器
    private MPJLambdaWrapper getWrapper(NbDriverEntity entity, Integer[] ids) {
        MPJLambdaWrapper<NbDriverEntity> wrapper = new MPJLambdaWrapper<NbDriverEntity>();
        wrapper.selectAll(NbDriverEntity.class)
                .eq(ObjectUtil.isNotNull(entity.getDriverId()), NbDriverEntity::getDriverId, entity.getDriverId())              // 司机id
                .like(StrUtil.isNotBlank(entity.getDriverName()), NbDriverEntity::getDriverName, entity.getDriverName())        //司机全名
                .like(StrUtil.isNotBlank(entity.getMobile()), NbDriverEntity::getMobile, entity.getMobile())                    //手机号
                .like(StrUtil.isNotBlank(entity.getEmail()), NbDriverEntity::getEmail, entity.getEmail())                       //邮箱
                .eq(ObjectUtil.isNotNull(entity.getBusinessType()), NbDriverEntity::getBusinessType, entity.getBusinessType())  //业务类型 1：派送；2退件；3派送和退件
                .eq(ObjectUtil.isNotNull(entity.getWorkTypeId()), NbDriverEntity::getWorkTypeId, entity.getWorkTypeId())       //工作方式 1：全职 2兼职
                .eq(ObjectUtil.isNotNull(entity.getIsValid()), NbDriverEntity::getIsValid, entity.getIsValid())                //是否启用
                .eq(ObjectUtil.isNotNull(entity.getAuditStatus()), NbDriverEntity::getAuditStatus, entity.getAuditStatus())    //审核状态
                .eq(ObjectUtil.isNotNull(entity.getIsLeader()), NbDriverEntity::getIsLeader, entity.getIsLeader())             // 是否司机队长
                .eq(ObjectUtil.isNotNull(entity.getPDriverId()), NbDriverEntity::getPDriverId, entity.getPDriverId())          //条件查询 所属队长
                .like(ObjectUtil.isNotNull(entity.getVehicleTypeId()), NbDriverEntity::getVehicleTypeId, entity.getVehicleTypeId()) //车辆类型
                .eq(ObjectUtil.isNotNull(entity.getCountryId()), NbDriverEntity::getCountryId, entity.getCountryId())         //国家
                .eq(ObjectUtil.isNotNull(entity.getProvinceId()), NbDriverEntity::getProvinceId, entity.getProvinceId())      //省份
                .eq(ObjectUtil.isNotNull(entity.getCityId()), NbDriverEntity::getCityId, entity.getCityId())                  //地区
                .like(StrUtil.isNotBlank(entity.getPostalCode()), NbDriverEntity::getPostalCode, entity.getPostalCode())      //邮编
                .eq(ObjectUtil.isNotNull(entity.getTcId()), NbDriverEntity::getTcId, entity.getTcId())                        //转运中心
                .eq(ObjectUtil.isNotNull(entity.getRoute4meMemberId()), NbDriverEntity::getRoute4meMemberId, entity.getRoute4meMemberId())    //route4me会员id
                .like(StrUtil.isNotBlank(entity.getFirstName()), NbDriverEntity::getFirstName, entity.getFirstName())           //名
                .like(StrUtil.isNotBlank(entity.getLastName()), NbDriverEntity::getLastName, entity.getLastName())              //姓
                .like(StrUtil.isNotBlank(entity.getPlateNumber()), NbDriverEntity::getPlateNumber, entity.getPlateNumber());    //车牌号码
        if (ObjectUtil.isNotNull(entity.getTcId()) && entity.getTcId() > 0) {
            // select tc_id id, center_name name from nb_transfer_center where tc_id > 0; ds=nbd;
            wrapper.select(NbTransferCenterEntity::getTcId)
                    .selectAs(NbTransferCenterEntity::getCenterName, NbDriverPageVo.Fields.transferCenterName);
        }
        wrapper.selectAs(CountriesEntity::getName, NbDriverPageVo.Fields.countriesName)
                .selectAs(StatesEntity::getName, NbDriverPageVo.Fields.statesName)
                .selectAs(CitiesEntity::getName, NbDriverPageVo.Fields.cityName)
                .selectAs(NbVehicleTypeEntity::getName, NbDriverPageVo.Fields.vehicleTypeName);
        if (ObjectUtil.isNotNull(entity.getPDriverId())) {
            wrapper.selectAs(NbDriverEntity::getDriverName, NbDriverPageVo.Fields.pDriverName);
        }
        wrapper.leftJoin(CountriesEntity.class, CountriesEntity::getId, NbDriverEntity::getCountryId)
                .leftJoin(StatesEntity.class, StatesEntity::getId, NbDriverEntity::getProvinceId)
                .leftJoin(CitiesEntity.class, CitiesEntity::getId, NbDriverEntity::getCityId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbDriverEntity::getTcId)
                .leftJoin(NbVehicleTypeEntity.class, NbVehicleTypeEntity::getVehicleTypeId, NbDriverEntity::getVehicleTypeId)
                .eq(ObjectUtil.isNotNull(entity.getCountryId()), NbDriverEntity::getCountryId, 39)
                .orderByDesc(NbDriverEntity::getDriverId);
        // .last("ORDER BY CASE audit_status WHEN 2 THEN 0 WHEN 1 THEN 1 WHEN 3 THEN 2 WHEN 4 THEN 3 END ASC");    //审核状态为2待审核时，排序在最前面
        wrapper.in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbDriverEntity::getDriverId, ids);
        return wrapper;
    }

    /**
     * 正向司机导出
     * @param entity
     * @param ids
     * @return
     */
    @Override
    public List<NbDriverExcelVo> getExcel(NbDriverEntity entity, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(entity, ids);
        return nbDriverMapper.selectJoinList(NbDriverExcelVo.class, wrapper);
    }

}