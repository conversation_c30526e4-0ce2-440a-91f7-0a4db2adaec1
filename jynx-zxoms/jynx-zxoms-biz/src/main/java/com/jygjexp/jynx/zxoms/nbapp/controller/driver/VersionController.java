package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.zxoms.entity.NbAppVersionAssignEntity;
import com.jygjexp.jynx.zxoms.entity.NbAppVersionEntity;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.send.service.NbAppVersionAssignService;
import com.jygjexp.jynx.zxoms.send.service.NbAppVersionService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: chenchang
 * @Description: app版本
 * @Date: 2024/11/12 0:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/version")
@Tag(description = "appdrivermisc", name = "APP-app版本")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class VersionController extends BaseController {
    private final NbAppVersionService appVersionService;
    private final NbAppVersionAssignService appVersionAssignService;

    public void current(@RequestParam("brand") String brand) {
        NbDriverEntity driver = getSmartLoginDriver();
        boolean isPda = true;
        if (StrUtil.isBlank(brand)) {
        } else {
            if ("idata".equalsIgnoreCase(brand) || "seuic".equalsIgnoreCase(brand) || "urovo".equalsIgnoreCase(brand)) {
                isPda = true;
            } else {
                isPda = false;
            }
        }

        int deviceType = 1;
        if (isPda == false) {
            deviceType = 2;
        }

        NbAppVersionEntity app = null;
        if (driver == null) {
            app = getPublicVersion(deviceType);
        } else {
            // "select * from nb_app_version where is_valid = true and device_type = ? order by app_id desc limit 1", deviceType);
            app = appVersionService.getOne(new LambdaQueryWrapper<NbAppVersionEntity>().eq(NbAppVersionEntity::getIsValid, true)
                    .eq(NbAppVersionEntity::getDeviceType, deviceType).orderByDesc(NbAppVersionEntity::getAppId));
            if (app != null && app.getIsGaryRelease()) {
                // 灰度的
                // "select * from nb_app_version_assign where app_id = ? and driver_id = ? limit 1", app.getAppId(), driver.getDriverId());
                NbAppVersionAssignEntity aa = appVersionAssignService.getOne(new LambdaQueryWrapper<NbAppVersionAssignEntity>().eq(NbAppVersionAssignEntity::getAppId, app.getAppId())
                        .eq(NbAppVersionAssignEntity::getDriverId, driver.getDriverId()));
                if (aa == null) {
                    app = getPublicVersion(deviceType);
                }
            }
        }
//		try {
//			Thread.sleep(5000);
//		} catch (InterruptedException e) {
//			e.printStackTrace();
//		}

        if (app == null) {
            renderAppSuc();
            return;
        }
        JSONObject appJo = new JSONObject();
        appJo.set("id", app.getAppId());
        appJo.set("versionName", app.getVersionName());
        appJo.set("versionCode", app.getVersionCode());
        appJo.set("createDate", DateFormatUtils.format(app.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
        appJo.set("download", app.getDownloadUrl());
        appJo.set("desc", app.getAppDesc());
        appJo.set("isFocus", app.getIsFocus());
        renderAppData(appJo);
    }

    /**
     * 获得最新公开版
     *
     * @param deviceType
     * @return
     */
    private NbAppVersionEntity getPublicVersion(int deviceType) {
        // "select * from nb_app_version where is_valid = true and is_gary_release = false and device_type = ? order by app_id desc limit 1", deviceType);
        return appVersionService.getOne(new LambdaQueryWrapper<NbAppVersionEntity>().eq(NbAppVersionEntity::getIsValid, true)
                .eq(NbAppVersionEntity::getIsGaryRelease, false).eq(NbAppVersionEntity::getDeviceType, deviceType).orderByDesc(NbAppVersionEntity::getAppId));
    }

}
