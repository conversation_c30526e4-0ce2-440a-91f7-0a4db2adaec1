package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderPathMapper;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathDeliveredService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.vo.NbOrderPathDeliveredPageVo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderPathExcelVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description:订单轨迹
 * @Date: 2024/10/18 20:26
 */
@Service
@RequiredArgsConstructor
public class NbOrderPathDeliveredServiceImpl extends ServiceImpl<NbOrderPathMapper,NbOrderPathEntity> implements NbOrderPathDeliveredService {
    private final NbOrderPathMapper nbOrderPathMapper;
    private final NbSortingCenterService nbSortingCenterService;
    private final RemoteUserService remoteUserService;

    /**
     * 签收记录分页查询
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<NbOrderPathDeliveredPageVo> search(Page page, NbOrderPathDeliveredPageVo vo) {
        //select sc_id id, center_name name from nb_sorting_center; ds=nbd;
        //select tc_id id, center_name name from nb_transfer_center; ds=nbd;
        //select driver_id id, concat(first_name, last_name) name from nb_driver; ds=nbd;
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return nbOrderPathMapper.selectJoinPage(page, NbOrderPathDeliveredPageVo.class, wrapper);
    }

    // 导出签收记录列表
    @Override
    public List<NbOrderPathExcelVo> getExcel(NbOrderPathDeliveredPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        return nbOrderPathMapper.selectJoinList(NbOrderPathExcelVo.class, wrapper);
    }

    private MPJLambdaWrapper getWrapper(NbOrderPathDeliveredPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbOrderPathEntity> wrapper = new MPJLambdaWrapper<NbOrderPathEntity>();
        String queryTime = vo.getQueryTime();   // 签收记录查询起止时间 如：2024-03-25 00:00:00-2024-12-31 00:00:00
        if (queryTime != null) {
            int splitIndex = queryTime.indexOf(":", queryTime.indexOf(":") + 1) + 3;
            String startTime = queryTime.substring(0, splitIndex);
            String endTime = queryTime.substring(splitIndex + 1);
            wrapper.ge(NbOrderPathEntity::getAddTime, startTime).le(NbOrderPathEntity::getAddTime, endTime);        //分页条件 签收记录查询起止时间
        }
        wrapper.selectAll(NbOrderPathEntity.class)
                .eq(ObjectUtil.isNotNull(vo.getOrderId()), NbOrderPathEntity::getOrderId, vo.getOrderId())      //分页条件 订单ID
                .eq(ObjectUtil.isNotNull(vo.getDriverId()), NbOrderPathEntity::getDriverId, vo.getDriverId());   //分页条件 司机ID

        if (ObjectUtil.isNotNull(vo.getDistance())) {
            switch (vo.getDistanceFlag()) {
                // 距离标识：1是等于，2是大于，3是小于，4是大于等于，5是小于等于
                case 1:
                    wrapper.eq(NbOrderPathEntity::getDistance, vo.getDistance());   //分页条件 离收件距离 =
                    break;
                case 2:
                    wrapper.gt(NbOrderPathEntity::getDistance, vo.getDistance());  // 离收件距离 >
                    break;
                case 3:
                    wrapper.lt(NbOrderPathEntity::getDistance, vo.getDistance());  // 离收件距离 <
                    break;
                case 4:
                    wrapper.ge(NbOrderPathEntity::getDistance, vo.getDistance());  // 离收件距离 >=
                    break;
                case 5:
                    wrapper.le(NbOrderPathEntity::getDistance, vo.getDistance()); // 离收件距离 <=
                    break;
            }
        }
        wrapper.selectAs(NbSortingCenterEntity::getCenterName, NbOrderPathDeliveredPageVo.Fields.transferCenterName)
                .selectAs(NbTransferCenterEntity::getCenterName, NbOrderPathDeliveredPageVo.Fields.sortingCenterName)
                .selectAs(NbOrderEntity::getPkgNo, NbOrderPathDeliveredPageVo.Fields.pkgNo)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getDriverName)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbOrderPathEntity::getScId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbOrderPathEntity::getTcId)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbOrderPathEntity::getDriverId)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderPathEntity::getOrderId)
                .eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_205_DELIVERED)  //只查询的是订单状态为205的签收记录
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbOrderPathEntity::getPathId, ids)
                .orderByDesc(NbOrderPathEntity::getPathId);

        SysUser user = remoteUserService.getOneUserById(SecurityUtils.getUser().getId()).getData();
        String nbdScId = user.getNbdScId();     //获取用户登录下的分拣中心
        if (StringUtils.isNotBlank(nbdScId)) {
            // 如果该用户配置了分拣中心，则取该用户下的分拣中心关联
            List<Integer> idList = Arrays.stream(nbdScId.split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(NbOrderEntity::getScId, idList);
        } else {
            // 如果该用户没有配置分拣中心，则取所有有订单数据的分拣中心关联，避免索引失效走全表扫描
            MPJLambdaWrapper<NbSortingCenterEntity> wrapper1 = new MPJLambdaWrapper<>();
            // 去重，避免重复的分拣中心
            wrapper1.leftJoin(NbOrderEntity.class, NbOrderEntity::getScId, NbSortingCenterEntity::getScId).select(NbSortingCenterEntity::getScId).distinct();
            List<NbSortingCenterEntity> scIdList = nbSortingCenterService.list(wrapper1);
            if (scIdList.size() == 0) {
                return wrapper;
            }
            List<Integer> collectScId = scIdList.stream().map(NbSortingCenterEntity::getScId).collect(Collectors.toList());
            wrapper.in(NbOrderPathEntity::getScId, collectScId);
        }
        return wrapper;
    }

}
