package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbPatchPathOrderEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbPatchPathOrderMapper;
import com.jygjexp.jynx.zxoms.send.service.NbPatchPathOrderService;
import org.springframework.stereotype.Service;

/**
 * 需要修复轨迹的订单
 *
 * <AUTHOR>
 * @date 2024-10-18 00:33:11
 */
@Service
public class NbPatchPathOrderServiceImpl extends ServiceImpl<NbPatchPathOrderMapper, NbPatchPathOrderEntity> implements NbPatchPathOrderService {

}