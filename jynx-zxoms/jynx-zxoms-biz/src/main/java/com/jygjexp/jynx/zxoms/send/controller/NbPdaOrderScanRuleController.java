package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbPdaOrderScanRuleEntity;
import com.jygjexp.jynx.zxoms.send.service.NbPdaOrderScanRuleService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * PDA订单扫描规则
 *
 * <AUTHOR>
 * @date 2024-10-14 16:05:31
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbPdaOrderScanRule" )
@Tag(description = "nbPdaOrderScanRule" , name = "PDA订单扫描规则管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbPdaOrderScanRuleController {

    private final NbPdaOrderScanRuleService nbPdaOrderScanRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param entity PDA订单扫描规则
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('pdaOrderScanRule_view')" )
    public R getNbPdaOrderScanRulePage(@ParameterObject Page page, @ParameterObject NbPdaOrderScanRuleEntity entity) {
        return R.ok(nbPdaOrderScanRuleService.search(page, entity));
    }


    /**
     * 通过id查询PDA订单扫描规则
     * @param ruleId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{ruleId}" )
    @PreAuthorize("@pms.hasPermission('pdaOrderScanRule_view')" )
    public R getById(@PathVariable("ruleId" ) Integer ruleId) {
        return R.ok(nbPdaOrderScanRuleService.getById(ruleId));
    }

    /**
     * 新增PDA订单扫描规则
     * @param basicNbPdaOrderScanRule PDA订单扫描规则
     * @return R
     */
    @Operation(summary = "新增PDA订单扫描规则" , description = "新增PDA订单扫描规则" )
    @SysLog("新增PDA订单扫描规则" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('pdaOrderScanRule_add')" )
    public R save(@RequestBody NbPdaOrderScanRuleEntity basicNbPdaOrderScanRule) {
        return R.ok(nbPdaOrderScanRuleService.save(basicNbPdaOrderScanRule));
    }

    /**
     * 修改PDA订单扫描规则
     * @param basicNbPdaOrderScanRule PDA订单扫描规则
     * @return R
     */
    @Operation(summary = "修改PDA订单扫描规则" , description = "修改PDA订单扫描规则" )
    @SysLog("修改PDA订单扫描规则" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('pdaOrderScanRule_edit')" )
    public R updateById(@RequestBody NbPdaOrderScanRuleEntity basicNbPdaOrderScanRule) {
        return R.ok(nbPdaOrderScanRuleService.updateById(basicNbPdaOrderScanRule));
    }

    /**
     * 通过id删除PDA订单扫描规则
     * @param ids ruleId列表
     * @return R
     */
    @Operation(summary = "通过id删除PDA订单扫描规则" , description = "通过id删除PDA订单扫描规则" )
    @SysLog("通过id删除PDA订单扫描规则" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('pdaOrderScanRule_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbPdaOrderScanRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicNbPdaOrderScanRule 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('pdaOrderScanRule_export')" )
    public List<NbPdaOrderScanRuleEntity> export(NbPdaOrderScanRuleEntity basicNbPdaOrderScanRule, Integer[] ids) {
        return nbPdaOrderScanRuleService.list(Wrappers.lambdaQuery(basicNbPdaOrderScanRule).in(ArrayUtil.isNotEmpty(ids), NbPdaOrderScanRuleEntity::getRuleId, ids));
    }
}