package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.mapper.NbShelfMapper;
import com.jygjexp.jynx.zxoms.send.mapper.NbShelfPkgLogMapper;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbShelfService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.vo.NbShelfExcelVo;

import com.jygjexp.jynx.zxoms.vo.ShelfPageVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Date;
import java.util.List;

/**
 * 自提存储柜（货架）
 *
 * <AUTHOR>
 * @date 2024-09-30 23:04:18
 */
@Service
@RequiredArgsConstructor
public class NbShelfServiceImpl extends ServiceImpl<NbShelfMapper, NbShelfEntity> implements NbShelfService {
    private final NbShelfMapper nbShelfMapper;
    private final NbShelfPkgLogMapper nbShelfPkgLogMapper;
    private final NbOrderService nbOrderService;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbOrderPathService nbOrderPathService;
    private final CommonDataUtil commonDataUtil;
    private final RemoteUserService remoteUserService;

    @Override
    public Page<ShelfPageVo> search(Page page, ShelfPageVo entity) {
        //select sc_id ID, center_name CN from nb_sorting_center; ds=nbd;
        //select tc_id, center_name, transfer_center_code code from nb_transfer_center; ds=nbd;
        //select id, login_id from eova_user; ds=eova;
        MPJLambdaWrapper wrapper = getWrapper(entity, null);
        return nbShelfMapper.selectJoinPage(page, ShelfPageVo.class, wrapper);
    }

    @Override
    public Long findByScId(Integer scId) {
        LambdaQueryWrapper<NbShelfEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NbShelfEntity::getScId,scId);
        return count(queryWrapper);
    }

    @Override
    public Long findByTcId(Integer tcId) {
        LambdaQueryWrapper<NbShelfEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NbShelfEntity::getTcId,tcId);
        return count(queryWrapper);
    }

    // 下架退件操作
    @Override
    public R markTo215(Integer logId) {
        NbShelfPkgLogEntity pkgLogEntity = nbShelfPkgLogMapper.selectById(logId);
        if (pkgLogEntity.getPickupTime() != null && pkgLogEntity.getPickupTime() > 0) {
            return LocalizedR.failed("nbshelf.package.has.been.taken.off.the.shelves", DateFormatUtils.format(pkgLogEntity.getPickupTime(), "yyyy-MM-dd HH:mm:ss"));
        }

        pkgLogEntity.setPickupTime(Instant.now().toEpochMilli());
        pkgLogEntity.setPickupDatetime(new Date());
        pkgLogEntity.setPickupDriverId(SecurityUtils.getUser().getId());
        pkgLogEntity.setUnshelveStaffId(SecurityUtils.getUser().getId());
        nbShelfPkgLogMapper.updateById(pkgLogEntity);

        NbOrderEntity order = nbOrderService.getById(pkgLogEntity.getOrderId());
        order.setOrderStatus(OrderDto.ORDER_STATUS_215_PARCEL_RETURN_TO_SENDER);
        nbOrderService.updateById(order);

        NbSortingCenterEntity sc = nbSortingCenterService.getById(order.getScId());
        String address = commonDataUtil.getAddress(sc.getProvinceId(), sc.getCityId());
        String timezone = sc.getScTimezone();

        NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), 0, 0d, 0d, address, timezone);
        op.setScId(order.getScId());
        op.setStaffId(SecurityUtils.getUser().getId());
        nbOrderPathService.save(op);
        return R.ok();
    }

    // 新增货架
    @Override
    public R addShelf(NbShelfEntity nbShelfEntity) {
        Integer scId = nbShelfEntity.getScId();
        if (scId > 0) {
            NbSortingCenterEntity sc = nbSortingCenterService.getById(scId);
            // "select count(1) cont from nb_shelf where sc_id = ?", scId).getInt("cont");
            Long total = findByScId(scId);
            String shelfCode = sc.getScCode() + "-" + String.format("%03d", (total + 1));
            // 检查 shelfCode 是否已存在
            if (this.isShelfCodeExists(shelfCode)) {
                return LocalizedR.failed("nbshelf.the.generated.shelf.code.already.exists", shelfCode);
            }
            nbShelfEntity.setShelfCode(shelfCode);
            nbShelfEntity.setAddTime(Instant.now().toEpochMilli());
            nbShelfEntity.setAddUserId(SecurityUtils.getUser().getId());
            nbShelfEntity.setAddUserName(SecurityUtils.getUser().getUsername());    // 设置创建人名称
        }
        return R.ok(nbShelfMapper.insert(nbShelfEntity));
    }

    public boolean isShelfCodeExists(String shelfCode) {
        LambdaQueryWrapper<NbShelfEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbShelfEntity::getShelfCode, shelfCode);
        return this.count(wrapper) > 0;
    }

    // 导出货架Excel
    @Override
    public List<NbShelfExcelVo> getExcel(ShelfPageVo entity, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(entity, ids);
        return nbShelfMapper.selectJoinList(NbShelfExcelVo.class, wrapper);
    }

    private MPJLambdaWrapper getWrapper(ShelfPageVo entity, Integer[] ids) {
        MPJLambdaWrapper<NbShelfEntity> wrapper = new MPJLambdaWrapper<NbShelfEntity>();
        // shelf_id desc 默认根据货架ID倒序排序
        wrapper.like(ObjectUtil.isNotNull(entity.getShelfCode()), NbShelfEntity::getShelfCode, entity.getShelfCode())   // 条件查询-货架编码
                .eq(ObjectUtil.isNotNull(entity.getScId()), NbShelfEntity::getScId,entity.getScId())    // 条件查询-分拣中心
                .eq(ObjectUtil.isNotNull(entity.getTcId()), NbShelfEntity::getTcId, entity.getTcId())   // 条件查询-转运中心
                .orderByDesc(NbShelfEntity::getShelfId);

        wrapper.selectAll(NbShelfEntity.class)
                .select(NbSortingCenterEntity::getScId)
                .selectAs(NbSortingCenterEntity::getCenterName, ShelfPageVo.Fields.sortingCenterName)
                .select(NbTransferCenterEntity::getTcId, NbTransferCenterEntity::getTransferCenterCode)
                .selectAs(NbTransferCenterEntity::getCenterName, ShelfPageVo.Fields.transferCenterName)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbShelfEntity::getScId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbShelfEntity::getTcId)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbShelfEntity::getShelfId, ids);
        String updateQueryTime = entity.getUpdateTime();
        if (updateQueryTime != null) {
            int splitIndex = updateQueryTime.indexOf(":", updateQueryTime.indexOf(":") + 1) + 3;
            String startAddTime = updateQueryTime.substring(0, splitIndex);
            String endAddTime = updateQueryTime.substring(splitIndex + 1);
            wrapper.ge(NbShelfEntity::getAddTime, startAddTime).le(NbShelfEntity::getAddTime, endAddTime);  // 条件查询-更新时间
        }

        wrapper.eq(NbShelfEntity::getTenantId, 1873269864720805890l);  // 条件查询-货架状态

        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbShelfEntity::getScId, idList);
        return wrapper;
    }

}