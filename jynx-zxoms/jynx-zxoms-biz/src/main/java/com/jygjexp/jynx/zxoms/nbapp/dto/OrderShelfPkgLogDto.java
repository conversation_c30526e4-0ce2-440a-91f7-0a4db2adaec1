package com.jygjexp.jynx.zxoms.nbapp.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/8 19:40
 */
@Data
@FieldNameConstants
public class OrderShelfPkgLogDto extends NbOrderEntity{
    @Schema(description = "上架时间")
    private Long putawayTime;   // 上架时间

    @Schema(description = "下架时间")
    private Long pickupTime;    // 下架时间

    @Schema(description = "上架状态")
    private String putawayCode; // 上架码

    @Schema(description = "上架人")
    private Integer pickupDriverId; // 上架人

    /**
     * 订单ID
     */
    @Schema(description="订单ID")
    private Integer orderId;


    /**
     * 司机ID
     */
    @Schema(description="司机ID")
    private Integer driverId;

    /**
     * 订单编号
     */
    @Schema(description="订单编号")
    private String orderNo;

    /**
     * 分拣中心ID
     */
    @Schema(description="分拣中心ID")
    private Integer scId;

    /**
     * 转运中心ID
     */
    @Schema(description="转运中心ID")
    private Integer tcId;

    /**
     * 所属大区ID
     */
    @Schema(description="所属大区ID")
    private Integer regionId;

    /**
     * 所属大区
     */
    @Schema(description="所属大区")
    private String regionCode;

    /**
     * 联系时间
     */
    @Schema(description="联系时间")
    private Date contactTime;


    /**
     * 订单类型
     */
    @Schema(description="订单类型")
    private String orderType;

    /**
     * 运输方式
     */
    @Schema(description="运输方式")
    private String transportWay;

    /**
     * 需要签字
     */
    @Schema(description="需要签字")
    private Boolean requiredSign;


    /**
     * 签收人
     */
    @Schema(description="签收人")
    private String destName;

    /**
     * 电话
     */
    @Schema(description="电话")
    private String destTel;



    /**
     * 包裹编号
     */
    @Schema(description="包裹编号")
    private String pkgNo;


    /**
     * 订单状态
     */
    @Schema(description="订单状态")
    private Integer orderStatus;

    /**
     * 线路号
     */
    @Schema(description="线路号")
    private Integer packId;

   /**
     * 配送尝试
     */
    @Schema(description="配送尝试")
    private Integer deliveryTry;

    /**
     * 配送类型：1=派送上门，2仓库自提，3驿站自提
     */
    @Schema(description="配送类型：1=派送上门，2仓库自提，3驿站自提")
    private Integer deliveryType;


}
