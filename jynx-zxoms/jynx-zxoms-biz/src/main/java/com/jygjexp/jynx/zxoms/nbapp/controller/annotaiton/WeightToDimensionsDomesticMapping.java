package com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton;

import com.jygjexp.jynx.zxoms.nbapp.validator.QuoteUomDomesticValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/8 13:53
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = QuoteUomDomesticValidator.class)
public @interface WeightToDimensionsDomesticMapping {
    String message() default "重量单位和尺寸单位不匹配: KG must correspond to CM. && LB must correspond to IN.";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
