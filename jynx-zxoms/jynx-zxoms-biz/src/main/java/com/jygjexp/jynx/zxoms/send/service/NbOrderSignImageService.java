package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbOrderSignImageEntity;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiOrderVo;
import com.jygjexp.jynx.zxoms.vo.NbOrderSignImagePageVo;

import java.util.List;

public interface NbOrderSignImageService extends IService<NbOrderSignImageEntity> {

    Page<NbOrderSignImagePageVo> search(Page page, NbOrderSignImageEntity nbOrderSignImage);

    // 根据订单ID查询订单签收图片
    List<NbOrderSignImageEntity> listOrderSignImageByOrderId(Integer orderId);

    List<ApiOrderVo> findImagesByOrderIds(List<Integer> orderIds);
}