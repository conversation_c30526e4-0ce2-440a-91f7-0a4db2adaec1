package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbDriverCostRuleItemEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbDriverCostRuleItemMapper;
import com.jygjexp.jynx.zxoms.send.service.NbDriverCostRuleItemService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 司机计费规则明细表
 *
 * <AUTHOR>
 * @date 2024-10-14 16:19:01
 */
@Service
@RequiredArgsConstructor
public class NbDriverCostRuleItemServiceImpl extends ServiceImpl<NbDriverCostRuleItemMapper, NbDriverCostRuleItemEntity> implements NbDriverCostRuleItemService {

}