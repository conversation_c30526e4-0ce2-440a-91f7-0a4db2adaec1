package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbSmsTemplateEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbSmsTemplateMapper;
import com.jygjexp.jynx.zxoms.send.service.NbSmsTemplateService;
import com.jygjexp.jynx.zxoms.vo.SmsTemplatePageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 短信模板
 *
 * <AUTHOR>
 * @date 2024-10-11 17:52:47
 */
@Service
@RequiredArgsConstructor
public class NbSmsTemplateServiceImpl extends ServiceImpl<NbSmsTemplateMapper, NbSmsTemplateEntity> implements NbSmsTemplateService {
    private final NbSmsTemplateMapper nbSmsTemplateMapper;

    // 短信模版分页查询
    @Override
    public Page<SmsTemplatePageVo> search(Page page, SmsTemplatePageVo vo) {
        LambdaQueryWrapper<NbSmsTemplateEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(vo.getTitle()), NbSmsTemplateEntity::getTitle, vo.getTitle())   // 条件查询-短信类型
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), NbSmsTemplateEntity::getIsValid, vo.getIsValid());    // 条件查询-是否启用
        String addTime = vo.getAddTime();   // 创建时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (addTime != null) {
            int splitIndex = addTime.indexOf(":", addTime.indexOf(":") + 1) + 3;
            String startTime = addTime.substring(0, splitIndex);
            String endTime = addTime.substring(splitIndex + 1);
            wrapper.ge(NbSmsTemplateEntity::getCreateTime, startTime).le(NbSmsTemplateEntity::getCreateTime, endTime);  // 条件查询-创建时间
        }
        wrapper.orderByDesc(NbSmsTemplateEntity::getCreateTime);
        return nbSmsTemplateMapper.selectPage(page, wrapper);
    }

}