package com.jygjexp.jynx.zxoms.nbapp.utils;

public class OrderTagUtil {
	
	/**
	 * 在路区的订单，且没有200状态，但是通过route4me标记为失败的
	 */
	public static String TAG_ORDER_FAILED_IN_TRANSFER_BATCH_BUT_NO_OS_200 = "1000000000";
	
	/**
	 * 计算是否满足某个标记条件
	 * @return
	 */
	public static boolean check(String ordeTag, String tag) {
		// 将二进制字符串转换为整数
        int num1 = Integer.parseInt(ordeTag, 2);
        int num2 = Integer.parseInt(tag, 2);

        // 进行按位与运算
        int result = num1 & num2;
        
        // 检查结果是否为零
        return result != 0;
	}
	
	/**
	 * 添加标记状态
	 * @return
	 */
	public static String bitwiseOr(String orderTag, String tag) {
        // 将二进制字符串转换为整数
        int num1 = Integer.parseInt(orderTag, 2);
        int num2 = Integer.parseInt(tag, 2);

        // 进行按位或运算
        int result = num1 | num2;

        // 将结果转换为二进制字符串，并补齐前导零
        String resultStr = Integer.toBinaryString(result);
        while (resultStr.length() < orderTag.length()) {
            resultStr = "0" + resultStr;
        }

        return resultStr;
    }

}
