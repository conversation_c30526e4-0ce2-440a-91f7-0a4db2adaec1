package com.jygjexp.jynx.zxoms.nbapp.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/7 13:52
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisUtil {
    private final RedisTemplate<String, String> redisTemplate;

    private static final Long RELEASE_SUCCESS = 1L;

    public static final int MINUTE_1 = 60;

    public static final int MINUTE_10 = MINUTE_1 * 10;

    public static final int HOUR_1 = MINUTE_1 * 60;

    public static final int HOUR_6 = HOUR_1 * 60;


    /**
     * 上锁
     * @param lockKey
     * @param requestId
     * @param expireTime
     * @return
     */
    public boolean tryLock(String lockKey, String requestId, int expireTime) {
        ValueOperations<String, String> ops = redisTemplate.opsForValue();
        Boolean success = ops.setIfAbsent(lockKey, requestId, expireTime, TimeUnit.SECONDS);

        if (Boolean.TRUE.equals(success)) {
            System.out.println("LOCK::" + lockKey + "->OK");
            return true;
        }

        log.info("LOCK::" + lockKey + "->FALSE");
        return false;
    }

    /**
     * 释放锁
     * @param lockKey
     * @param requestId
     * @return
     */
    public boolean releaseLock(String lockKey, String requestId) {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                "return redis.call('del', KEYS[1]) " +
                "else return 0 end";

        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(script);
        redisScript.setResultType(Long.class);

        Long result = redisTemplate.execute(redisScript, Collections.singletonList(lockKey), requestId);

        log.info("LOCK_RELEASE::" + lockKey + "->" + result);

        return RELEASE_SUCCESS.equals(result);
    }

    /**
     * 锁等待
     * @param lockKey
     * @param requestId
     * @param expireTime
     * @param awaitSecond
     * @return
     */
    public boolean tryLockAwait(String lockKey, String requestId, int expireTime, int awaitSecond) {
        Long WAIT_MILLIS_PER = 200L;
        TimeUnit seconds = TimeUnit.SECONDS;
        long waitMillis = seconds.toMillis(awaitSecond);

        long waitAlready = 0;

        while (tryLock(lockKey, requestId, expireTime) == false && waitAlready < waitMillis) {
            try {
                Thread.sleep(WAIT_MILLIS_PER);
            } catch (InterruptedException e) {
                e.printStackTrace();
                log.error("Interrupted when trying to get a lock. key: " + lockKey);
            }
            waitAlready += WAIT_MILLIS_PER;
        }
        if (waitAlready < waitMillis) {
            return true;
        }
        log.warn("<====== lock " + lockKey + " failed after waiting for " + waitAlready + " ms");
        return false;
    }

}
