package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.entity.NbPriceDistrictEntity;
import com.jygjexp.jynx.zxoms.send.service.NbPriceDistrictService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 价格区域
 *
 * <AUTHOR>
 * @date 2024-11-08 09:56:07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbPriceDistrict" )
@Tag(description = "nbPriceDistrict" , name = "价格区域管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbPriceDistrictController {

    private final  NbPriceDistrictService nbPriceDistrictService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbPriceDistrict 价格区域
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceDistrict_view')" )
    public R getNbPriceDistrictPage(@ParameterObject Page page, @ParameterObject NbPriceDistrictEntity nbPriceDistrict) {
        nbPriceDistrict.setCreateUserId(SecurityUtils.getUser().getId());
        return R.ok(nbPriceDistrictService.search(page, nbPriceDistrict));
    }


    /**
     * 通过id查询价格区域
     * @param pdId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{pdId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceDistrict_view')" )
    public R getById(@PathVariable("pdId" ) Integer pdId) {
        return R.ok(nbPriceDistrictService.getById(pdId));
    }

    /**
     * 新增价格区域
     * @param nbPriceDistrict 价格区域
     * @return R
     */
    @Operation(summary = "新增价格区域" , description = "新增价格区域" )
    @SysLog("新增价格区域" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceDistrict_add')" )
    public R save(@RequestBody NbPriceDistrictEntity nbPriceDistrict) {
        return R.ok(nbPriceDistrictService.save(nbPriceDistrict));
    }

    /**
     * 修改价格区域
     * @param nbPriceDistrict 价格区域
     * @return R
     */
    @Operation(summary = "修改价格区域" , description = "修改价格区域" )
    @SysLog("修改价格区域" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceDistrict_edit')" )
    public R updateById(@RequestBody NbPriceDistrictEntity nbPriceDistrict) {
        return R.ok(nbPriceDistrictService.updateById(nbPriceDistrict));
    }

    /**
     * 通过id删除价格区域
     * @param ids pdId列表
     * @return R
     */
    @Operation(summary = "通过id删除价格区域" , description = "通过id删除价格区域" )
    @SysLog("通过id删除价格区域" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceDistrict_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbPriceDistrictService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbPriceDistrict 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbPriceDistrict_export')" )
    public List<NbPriceDistrictEntity> export(NbPriceDistrictEntity nbPriceDistrict,Integer[] ids) {
        return nbPriceDistrictService.list(Wrappers.lambdaQuery(nbPriceDistrict).in(ArrayUtil.isNotEmpty(ids), NbPriceDistrictEntity::getPdId, ids));
    }
}