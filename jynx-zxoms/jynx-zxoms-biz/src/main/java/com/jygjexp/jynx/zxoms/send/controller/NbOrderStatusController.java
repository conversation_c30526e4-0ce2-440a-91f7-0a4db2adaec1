package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatusEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;

/**
 * 订单状态维护
 *
 * <AUTHOR>
 * @date 2024-10-14 15:50:03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderStatus" )
@Tag(description = "nbOrderStatus" , name = "订单状态维护管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderStatusController {

    private final NbOrderStatusService nbOrderStatusService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param basicNbOrderStatus 订单状态维护
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStatus_view')" )
    public R getNbOrderStatusPage(@ParameterObject Page page, @ParameterObject NbOrderStatusEntity basicNbOrderStatus) {
        LambdaQueryWrapper<NbOrderStatusEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtil.isNotNull(basicNbOrderStatus.getId()), NbOrderStatusEntity::getId, basicNbOrderStatus.getId())
                .eq(ObjectUtil.isNotEmpty(basicNbOrderStatus.getStatusType()), NbOrderStatusEntity::getStatusType, basicNbOrderStatus.getStatusType())
                .like(ObjectUtil.isNotNull(basicNbOrderStatus.getTitle()), NbOrderStatusEntity::getTitle, basicNbOrderStatus.getTitle())
                .orderByDesc(NbOrderStatusEntity::getAddTime);
        return R.ok(nbOrderStatusService.page(page, wrapper));
    }


    /**
     * 通过id查询订单状态维护
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStatus_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(nbOrderStatusService.getById(id));
    }

    /**
     * 新增订单状态维护
     * @param basicNbOrderStatus 订单状态维护
     * @return R
     */
    @Operation(summary = "新增订单状态维护" , description = "新增订单状态维护" )
    @SysLog("新增订单状态维护" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStatus_add')" )
    public R save(@RequestBody NbOrderStatusEntity basicNbOrderStatus) throws ParseException {
        return R.ok(nbOrderStatusService.save(basicNbOrderStatus));
    }

    /**
     * 修改订单状态维护
     * @param basicNbOrderStatus 订单状态维护
     * @return R
     */
    @Operation(summary = "修改订单状态维护" , description = "修改订单状态维护" )
    @SysLog("修改订单状态维护" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStatus_edit')" )
    public R updateById(@RequestBody NbOrderStatusEntity basicNbOrderStatus) {
        return R.ok(nbOrderStatusService.updateById(basicNbOrderStatus));
    }

    /**
     * 通过id删除订单状态维护
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除订单状态维护" , description = "通过id删除订单状态维护" )
    @SysLog("通过id删除订单状态维护" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStatus_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderStatusService.removeBatchByIds(CollUtil.toList(ids)));
    }

    @Operation(summary = "查询全部订单状态" , description = "查询全部订单状态" )
    @GetMapping("/orderStatusNameList")
    public R orderStatusNameList() {
        LambdaQueryWrapper<NbOrderStatusEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(NbOrderStatusEntity::getId, NbOrderStatusEntity::getTitle).groupBy(NbOrderStatusEntity::getId);
        return R.ok(nbOrderStatusService.list(wrapper));
    }

    /**
     * 导出excel 表格
     * @param basicNbOrderStatus 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStatus_export')" )
    public List<NbOrderStatusEntity> export(NbOrderStatusEntity basicNbOrderStatus, Integer[] ids) {
        return nbOrderStatusService.list(Wrappers.lambdaQuery(basicNbOrderStatus).in(ArrayUtil.isNotEmpty(ids), NbOrderStatusEntity::getId, ids));
    }
}