package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbShelfPkgLogEntity;
import com.jygjexp.jynx.zxoms.send.vo.NbShelfPkgLogExcelVo;
import com.jygjexp.jynx.zxoms.vo.NbShelfPkgLogPageVo;

import java.util.List;

public interface NbShelfPkgLogService extends IService<NbShelfPkgLogEntity> {
    Page<NbShelfPkgLogPageVo> search(Page page, NbShelfPkgLogPageVo shelfPkgLogPageVo);

    List<NbShelfPkgLogExcelVo> getExcel(NbShelfPkgLogPageVo vo, Integer[] ids); // 在架包裹导出Excel
}