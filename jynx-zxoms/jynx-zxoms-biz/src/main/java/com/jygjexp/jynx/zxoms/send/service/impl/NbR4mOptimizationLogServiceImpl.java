package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbR4mOptimizationLogEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbR4mOptimizationLogMapper;
import com.jygjexp.jynx.zxoms.send.service.NbR4mOptimizationLogService;
import org.springframework.stereotype.Service;
/**
 * Route4Me优化记录
 *
 * <AUTHOR>
 * @date 2024-10-15 23:48:00
 */
@Service
public class NbR4mOptimizationLogServiceImpl extends ServiceImpl<NbR4mOptimizationLogMapper, NbR4mOptimizationLogEntity> implements NbR4mOptimizationLogService {
}