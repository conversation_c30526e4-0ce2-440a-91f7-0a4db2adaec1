package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.json.JSONObject;
import com.jygjexp.jynx.zxoms.send.constants.NBDConstants;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbUserUploadFileEntity;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.vo.APPDriverUploadVo;
import com.jygjexp.jynx.zxoms.send.service.NbUserUploadFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.Instant;

/**
 * @Author: chenchang
 * @Description: 司机上传文件相关
 * @Date: 2024/11/12 9:25
 */

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/upload" )
@Tag(description = "appdriverupload" , name = "APP-司机上传文件" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DriverUploadController extends BaseController {
    private final NbUserUploadFileService userUploadFileService;

    @Operation(summary = "index" , description = "index" )
    @PostMapping("/index" )
    public void index(@RequestBody APPDriverUploadVo vo) {
        MultipartFile file = vo.getFile();
        System.out.println(file);

        String type = vo.getType();

        NbDriverEntity driver = getLoginDriver();
        JSONObject jo = new JSONObject();
        String ymdhms = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
        if ("driver".equals(type)) {
            // 保险单
            String fileName = vo.getFileName();

            String uri = "/driver/personal/" + driver.getDriverId() + "/" + fileName + "." + ymdhms + ".jpg";
            String path = NBDConstants.SIGN_IMAGE_PATH + uri;

            File targetFile = new File(path);
            if (!targetFile.getParentFile().exists()) {
                targetFile.getParentFile().mkdirs();
                targetFile.getParentFile().setExecutable(true, false);
                targetFile.getParentFile().setReadable(true, false);
            }

            try {
                FileUtils.moveFile((File) file, targetFile);
                targetFile.setExecutable(true, false);
                targetFile.setReadable(true, false);
            } catch (IOException e) {
                e.printStackTrace();
            }

            jo.set("url", NBDConstants.IMAGE_URL + uri);
            renderAppData(jo);
            return;
        } else if ("pickup".equals(type)) {
            Long oriFileSize = file.getSize();
            String orderId = vo.getOrderId();
            String random = RandomStringUtils.randomAlphanumeric(8);

            String uri = "/driver/pickup/" + driver.getDriverId() + "/" + orderId + "." + ymdhms + "." + random + ".jpg";
            String path = NBDConstants.SIGN_IMAGE_PATH + uri;

            File targetFile = new File(path);
            if (!targetFile.getParentFile().exists()) {
                targetFile.getParentFile().mkdirs();
                targetFile.getParentFile().setExecutable(true, false);
                targetFile.getParentFile().setReadable(true, false);
            }

            try {
                FileUtils.moveFile((File) file, targetFile);
                targetFile.setExecutable(true, false);
                targetFile.setReadable(true, false);
            } catch (IOException e) {
                e.printStackTrace();
            }

            String thunmUri = "/driver/pickup/" + driver.getDriverId() + "/" + orderId + "." + ymdhms + "." + random + "_thumb.jpg";
            String thumbPath = NBDConstants.SIGN_IMAGE_PATH + thunmUri;
            File thumbImage = new File(thumbPath);

            try {
                Thumbnails.of(targetFile)
                        .size(600, 600)
                        .outputQuality(0.8d)
                        .toFile(thumbImage);

                thumbImage.setExecutable(true, false);
                thumbImage.setReadable(true, false);
            } catch (IOException e) {
                e.printStackTrace();
            }

            String fileUrl = NBDConstants.IMAGE_URL + uri;
            String thumbUrl = NBDConstants.IMAGE_URL + thunmUri;
            jo.set("url", fileUrl);

            NbUserUploadFileEntity uuf = new NbUserUploadFileEntity();
            uuf.setDriverId(driver.getDriverId());
            uuf.setUploadTime(Instant.now().toEpochMilli());
            uuf.setFileName(file.getName());
            uuf.setFileSize(oriFileSize);
            uuf.setFilePath(path);
            uuf.setFileUrl(fileUrl);
            uuf.setThumbPath(thumbPath);
            uuf.setThumbUrl(thumbUrl);
            uuf.setThumbSize(thumbImage.length());
            uuf.setUploadType(type);
            userUploadFileService.save(uuf);

            jo.set("thumb", thumbUrl);
            jo.set("fileId", uuf.getFileId());
            renderAppData(jo);
        }
    }
}
