package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbDriverScanedLqReportEntity;
import com.jygjexp.jynx.zxoms.send.service.NbDriverScanedLqReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 扫描后提交的批次针对路区的报表
 *
 * <AUTHOR>
 * @date 2024-11-02 21:23:39
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbDriverScanedLqReport" )
@Tag(description = "nbDriverScanedLqReport" , name = "扫描后提交的批次针对路区的报表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbDriverScanedLqReportController {

    private final  NbDriverScanedLqReportService nbDriverScanedLqReportService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbDriverScanedLqReport 扫描后提交的批次针对路区的报表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/search" )
    @PreAuthorize("@pms.hasPermission('driverScanedLqReport_view')" )
    public R getNbDriverScanedLqReportPage(@ParameterObject Page page, @ParameterObject NbDriverScanedLqReportEntity nbDriverScanedLqReport) {
        return R.ok(nbDriverScanedLqReportService.search(page, nbDriverScanedLqReport));
    }


    /**
     * 通过id查询扫描后提交的批次针对路区的报表
     * @param reportId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{reportId}" )
    @PreAuthorize("@pms.hasPermission('driverScanedLqReport_view')" )
    public R getById(@PathVariable("reportId" ) Integer reportId) {
        return R.ok(nbDriverScanedLqReportService.getById(reportId));
    }

    /**
     * 新增扫描后提交的批次针对路区的报表
     * @param nbDriverScanedLqReport 扫描后提交的批次针对路区的报表
     * @return R
     */
    @Operation(summary = "新增扫描后提交的批次针对路区的报表" , description = "新增扫描后提交的批次针对路区的报表" )
    @SysLog("新增扫描后提交的批次针对路区的报表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('driverScanedLqReport_add')" )
    public R save(@RequestBody NbDriverScanedLqReportEntity nbDriverScanedLqReport) {
        return R.ok(nbDriverScanedLqReportService.save(nbDriverScanedLqReport));
    }

    /**
     * 修改扫描后提交的批次针对路区的报表
     * @param nbDriverScanedLqReport 扫描后提交的批次针对路区的报表
     * @return R
     */
    @Operation(summary = "修改扫描后提交的批次针对路区的报表" , description = "修改扫描后提交的批次针对路区的报表" )
    @SysLog("修改扫描后提交的批次针对路区的报表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('driverScanedLqReport_edit')" )
    public R updateById(@RequestBody NbDriverScanedLqReportEntity nbDriverScanedLqReport) {
        return R.ok(nbDriverScanedLqReportService.updateById(nbDriverScanedLqReport));
    }

    /**
     * 通过id删除扫描后提交的批次针对路区的报表
     * @param ids reportId列表
     * @return R
     */
    @Operation(summary = "通过id删除扫描后提交的批次针对路区的报表" , description = "通过id删除扫描后提交的批次针对路区的报表" )
    @SysLog("通过id删除扫描后提交的批次针对路区的报表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('driverScanedLqReport_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbDriverScanedLqReportService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbDriverScanedLqReport 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('driverScanedLqReport_export')" )
    public List<NbDriverScanedLqReportEntity> export(NbDriverScanedLqReportEntity nbDriverScanedLqReport,Integer[] ids) {
        return nbDriverScanedLqReportService.list(Wrappers.lambdaQuery(nbDriverScanedLqReport).in(ArrayUtil.isNotEmpty(ids), NbDriverScanedLqReportEntity::getReportId, ids));
    }
}