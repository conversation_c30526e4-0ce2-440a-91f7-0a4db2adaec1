package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbTransferBatchCostModifyLogEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbTransferBatchCostModifyLogMapper;
import com.jygjexp.jynx.zxoms.send.service.NbTransferBatchCostModifyLogService;
import org.springframework.stereotype.Service;
/**
 * 批次价格修改记录（路区补贴记录）
 *
 * <AUTHOR>
 * @date 2024-10-12 22:44:16
 */
@Service
public class NbTransferBatchCostModifyLogServiceImpl extends ServiceImpl<NbTransferBatchCostModifyLogMapper, NbTransferBatchCostModifyLogEntity> implements NbTransferBatchCostModifyLogService {
}