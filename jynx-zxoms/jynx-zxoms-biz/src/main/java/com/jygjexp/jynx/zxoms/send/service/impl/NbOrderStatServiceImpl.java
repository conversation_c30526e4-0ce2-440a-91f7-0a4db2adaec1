package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderStatMapper;
import com.jygjexp.jynx.zxoms.send.service.NbOrderStatService;
import com.jygjexp.jynx.zxoms.vo.OrderStatPageVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单统计
 *
 * <AUTHOR>
 * @date 2024-10-14 15:55:56
 */
@Service
@RequiredArgsConstructor
public class NbOrderStatServiceImpl extends ServiceImpl<NbOrderStatMapper, NbOrderStatEntity> implements NbOrderStatService {
	private final NbOrderStatMapper nbOrderStatMapper;

	/**
	 * 订单统计分页查询
	 * @param page
	 * @param vo
	 * @return
	 */
    @Override
    public Page<OrderStatPageVo> orderStat(Page page, OrderStatPageVo vo) {
		Integer pageNo = vo.getPageNo();
		Integer pageSize = vo.getPageSize();
		Integer scId = vo.getSortingCenterId();
		String dateRange = vo.getDateRange();

		// paginate(pageNo, pageSize, "select *", "from nb_order_stat where sc_id = ? " + where + " order by stat_id desc", scId);
		MPJLambdaWrapper<NbOrderStatEntity> wrapper = new MPJLambdaWrapper<>();
		wrapper.eq(NbOrderStatEntity::getScId, scId);
		if (StrUtil.isNotBlank(dateRange) && dateRange.contains("-")) {
			String[] dates = dateRange.split("-");
			String startDate = dates[0].trim(); // 开始日期
			String endDate = dates[1].trim();   // 结束日期

			// 设置日期范围条件
			wrapper.ge(NbOrderStatEntity::getStatDate, startDate).le(NbOrderStatEntity::getStatDate, endDate);
			pageSize = 100;
		}
		wrapper.orderByDesc(NbOrderStatEntity::getStatId);
		Page joinPage = nbOrderStatMapper.selectJoinPage(new Page<>(pageNo, pageSize), OrderStatPageVo.class, wrapper);
		List<OrderStatPageVo> records = joinPage.getRecords();
		JSONArray ja = records.stream().map(ot -> {
			JSONObject jo = new JSONObject();
			jo.set("statId", ot.getStatId());
			jo.set("scId", ot.getScId());
			jo.set("statDate", DateFormatUtils.format(ot.getStatDate(), "yyyy-MM-dd"));
			jo.set("orderTotal", ot.getAddOrderTotal());
			jo.set("s190", ot.getS190());
			jo.set("s200", ot.getS200());
			jo.set("s201", ot.getS201());
			jo.set("s204", ot.getS204());
			jo.set("s205", ot.getS205());
			jo.set("sFail", ot.getSFail());
			jo.set("s225", ot.getS225());
			jo.set("s236", ot.getS236());
			jo.set("s237", ot.getS237());
			jo.set("s290", ot.getS290());
			jo.set("s340", ot.getS340());
			jo.set("other", ot.getAddOrderTotal() - ot.getS190() - ot.getS200() - ot.getS201() - ot.getS204() - ot.getS205() - ot.getS225() - ot.getS236() - ot.getS237() - ot.getS290() - ot.getS340() - ot.getSFail());
			return jo;
		}).collect(Collectors.toCollection(JSONArray::new));
		joinPage.setRecords(ja);

		return joinPage;
    }

}