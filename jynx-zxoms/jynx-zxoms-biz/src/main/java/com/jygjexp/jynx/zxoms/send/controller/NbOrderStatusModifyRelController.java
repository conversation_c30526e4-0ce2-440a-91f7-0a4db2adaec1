package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatusModifyRelEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderStatusModifyRelService;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单修改关系
 *
 * <AUTHOR>
 * @date 2024-10-14 15:31:44
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderStatusModifyRel" )
@Tag(description = "nbOrderStatusModifyRel" , name = "订单修改关系管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderStatusModifyRelController {
    private final NbOrderStatusModifyRelService nbOrderStatusModifyRelService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param orderStatusModifyRelEntity 订单修改关系
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('orderStatusModifyRel_view')" )
    public R getNbOrderStatusModifyRelPage(@ParameterObject Page page, @ParameterObject NbOrderStatusModifyRelEntity orderStatusModifyRelEntity) {
        LambdaQueryWrapper<NbOrderStatusModifyRelEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtil.isNotNull(orderStatusModifyRelEntity.getFromStatus()), NbOrderStatusModifyRelEntity::getFromStatus, orderStatusModifyRelEntity.getFromStatus());
        wrapper.eq(ObjectUtil.isNotNull(orderStatusModifyRelEntity.getStatusType()), NbOrderStatusModifyRelEntity::getStatusType, orderStatusModifyRelEntity.getStatusType());
        wrapper.orderByDesc(NbOrderStatusModifyRelEntity::getAddTime);
        return R.ok(nbOrderStatusModifyRelService.page(page, wrapper));
    }

    /**
     * 查询订单状态维护目标状态
     * @param fromStatus 当前状态
     * @return
     */
    @Operation(summary = "查询订单状态维护目标状态" , description = "查询订单状态维护目标状态" )
    @PostMapping("/listOrderStatus" )
    public R listOrderStatus(@Parameter(description = "当前状态") @RequestParam(value = "fromStatus", required = false) Integer fromStatus){
        return R.ok(nbOrderStatusModifyRelService.listTargetOrderStatus(fromStatus));
    }

    /**
     * 通过id查询订单修改关系
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('orderStatusModifyRel_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(nbOrderStatusModifyRelService.getById(id));
    }

    /**
     * 新增订单修改关系
     * @param orderStatusModifyRel 订单修改关系
     * @return R
     */
    @Operation(summary = "新增订单修改关系" , description = "新增订单修改关系" )
    @SysLog("新增订单修改关系" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('orderStatusModifyRel_add')" )
    public R save(@RequestBody NbOrderStatusModifyRelEntity orderStatusModifyRel) {
        if (ObjectUtil.isNotNull(orderStatusModifyRel.getFromStatus()) && orderStatusModifyRel.getFromStatus() >= 1
                && orderStatusModifyRel.getFromStatus() <= 189){
            orderStatusModifyRel.setStatusType(2);   // 退件类型 1-189
        } else if (ObjectUtil.isNotNull(orderStatusModifyRel.getFromStatus()) && orderStatusModifyRel.getFromStatus() >= 190
                && orderStatusModifyRel.getFromStatus() <= 300) {
            orderStatusModifyRel.setStatusType(1);   // 派送类型 190-300
        }
        return R.ok(nbOrderStatusModifyRelService.save(orderStatusModifyRel));
    }

    /**
     * 修改订单修改关系
     * @param basicNbOrderStatusModifyRel 订单修改关系
     * @return R
     */
    @Operation(summary = "修改订单修改关系" , description = "修改订单修改关系" )
    @SysLog("修改订单修改关系" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('orderStatusModifyRel_edit')" )
    public R updateById(@RequestBody NbOrderStatusModifyRelEntity basicNbOrderStatusModifyRel) {
        return R.ok(nbOrderStatusModifyRelService.updateById(basicNbOrderStatusModifyRel));
    }

    /**
     * 通过id删除订单修改关系
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除订单修改关系" , description = "通过id删除订单修改关系" )
    @SysLog("通过id删除订单修改关系" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('orderStatusModifyRel_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderStatusModifyRelService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicNbOrderStatusModifyRel 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('orderStatusModifyRel_export')" )
    public List<NbOrderStatusModifyRelEntity> export(NbOrderStatusModifyRelEntity basicNbOrderStatusModifyRel, Integer[] ids) {
        return nbOrderStatusModifyRelService.list(Wrappers.lambdaQuery(basicNbOrderStatusModifyRel).in(ArrayUtil.isNotEmpty(ids), NbOrderStatusModifyRelEntity::getId, ids));
    }
}