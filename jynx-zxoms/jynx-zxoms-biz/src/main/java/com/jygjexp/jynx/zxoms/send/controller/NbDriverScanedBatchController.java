package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbDriverScanedBatchEntity;
import com.jygjexp.jynx.zxoms.send.service.NbDriverScanedBatchService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 扫描的批次
 *
 * <AUTHOR>
 * @date 2024-11-11 23:06:00
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbDriverScanedBatch" )
//@Tag(description = "nbDriverScanedBatch" , name = "扫描的批次管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbDriverScanedBatchController {

    private final  NbDriverScanedBatchService nbDriverScanedBatchService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbDriverScanedBatch 扫描的批次
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScanedBatch_view')" )
    public R getNbDriverScanedBatchPage(@ParameterObject Page page, @ParameterObject NbDriverScanedBatchEntity nbDriverScanedBatch) {
        LambdaQueryWrapper<NbDriverScanedBatchEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbDriverScanedBatchService.page(page, wrapper));
    }


    /**
     * 通过id查询扫描的批次
     * @param batchId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{batchId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScanedBatch_view')" )
    public R getById(@PathVariable("batchId" ) Integer batchId) {
        return R.ok(nbDriverScanedBatchService.getById(batchId));
    }

    /**
     * 新增扫描的批次
     * @param nbDriverScanedBatch 扫描的批次
     * @return R
     */
//    @Operation(summary = "新增扫描的批次" , description = "新增扫描的批次" )
//    @SysLog("新增扫描的批次" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScanedBatch_add')" )
    public R save(@RequestBody NbDriverScanedBatchEntity nbDriverScanedBatch) {
        return R.ok(nbDriverScanedBatchService.save(nbDriverScanedBatch));
    }

    /**
     * 修改扫描的批次
     * @param nbDriverScanedBatch 扫描的批次
     * @return R
     */
//    @Operation(summary = "修改扫描的批次" , description = "修改扫描的批次" )
//    @SysLog("修改扫描的批次" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScanedBatch_edit')" )
    public R updateById(@RequestBody NbDriverScanedBatchEntity nbDriverScanedBatch) {
        return R.ok(nbDriverScanedBatchService.updateById(nbDriverScanedBatch));
    }

    /**
     * 通过id删除扫描的批次
     * @param ids batchId列表
     * @return R
     */
//    @Operation(summary = "通过id删除扫描的批次" , description = "通过id删除扫描的批次" )
//    @SysLog("通过id删除扫描的批次" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScanedBatch_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbDriverScanedBatchService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbDriverScanedBatch 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScanedBatch_export')" )
    public List<NbDriverScanedBatchEntity> export(NbDriverScanedBatchEntity nbDriverScanedBatch,Integer[] ids) {
        return nbDriverScanedBatchService.list(Wrappers.lambdaQuery(nbDriverScanedBatch).in(ArrayUtil.isNotEmpty(ids), NbDriverScanedBatchEntity::getBatchId, ids));
    }
}