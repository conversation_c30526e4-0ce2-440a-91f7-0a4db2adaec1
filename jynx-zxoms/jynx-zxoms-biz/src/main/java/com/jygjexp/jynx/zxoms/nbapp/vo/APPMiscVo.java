package com.jygjexp.jynx.zxoms.nbapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/11 21:39
 */
@Data
public class APPMiscVo {
    @NotNull(message = "订单ID不能为空")
    @Schema(description = "订单ID")
    private Integer orderId;

    @NotNull(message = "模板ID不能为空")
    @Schema(description = "模板ID")
    private Integer templateId;

}
