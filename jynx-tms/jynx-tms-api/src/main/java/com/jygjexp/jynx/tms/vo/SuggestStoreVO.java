package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Schema(description = "推荐门店VO")
public class SuggestStoreVO {

    @Schema(description = "门店id")
    private Long id;

    @Schema(description = "门店代码")
    private String storeCode;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "门店地址")
    private String storeAddress;

    @Schema(description = "距离")
    private Double distance;

}
