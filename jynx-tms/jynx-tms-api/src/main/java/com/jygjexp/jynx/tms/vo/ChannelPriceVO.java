package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Schema(description = "服务商报价")
public class ChannelPriceVO {

    @Schema(description = "服务商渠道代码")
    private String providerCode;

    @Schema(description = "服务商名称")
    private String providerName;

    @Schema(description = "服务商服务方式")
    private String providerServiceWay;

    @Schema(description = "服务商服务时效")
    private String providerTransportTime;

    @Schema(description = "运费")
    private BigDecimal freightAmount;

    @Schema(description = "保费")
    private BigDecimal insuranceAmount;

    @Schema(description = "POD/面签费")
    private BigDecimal podAmount;

    @Schema(description = "税费")
    private BigDecimal taxAmount;
}
