package com.jygjexp.jynx.tms.vo;

import com.jygjexp.jynx.tms.entity.TmsBlindBoxEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceQuoteEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * 物流商盲盒信息
 *
 * <AUTHOR>
 * @date 2025-07-18 18:08:07
 */
@Data
@FieldNameConstants
@Schema(description = "物流商盲盒信息")
public class TmsBlindBoxPageVo extends TmsBlindBoxEntity {

    /**
     * 创建时间开始
     */
    @Schema(description="创建时间开始")
    private String startTime;

    /**
     * 创建时间结束
     */
    @Schema(description="创建时间结束")
    private String endTime;

}