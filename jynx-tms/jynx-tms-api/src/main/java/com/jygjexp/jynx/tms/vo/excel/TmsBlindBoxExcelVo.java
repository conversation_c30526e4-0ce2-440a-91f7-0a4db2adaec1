package com.jygjexp.jynx.tms.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.jygjexp.jynx.tms.annotation.ConvertType;
import com.jygjexp.jynx.tms.utils.IntegerDictsConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 盲盒信息导出
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10)
@Schema(description = "盲盒信息导出")
public class TmsBlindBoxExcelVo {

    /**
     * 盲盒代码
     */
    @ColumnWidth(15)
    @ExcelProperty("(Blind box code)盲盒代码")
    @Schema(description="盲盒代码")
    private String code;

    /**
     * 盲盒名称
     */
    @ColumnWidth(15)
    @ExcelProperty("(Blind box name)盲盒名称")
    @Schema(description="盲盒名称")
    private String name;

    /**
     * 比价规则
     */
    @ConvertType("boxRule")
    @ColumnWidth(15)
    @ExcelProperty(value = "(Price Comparison Rules)比价规则",converter =  IntegerDictsConverter.class)
    @Schema(description="比价规则")
    private Integer compareRule;

    /**
     * 启用状态
     */
    @ConvertType("tmsIsValid")
    @ColumnWidth(10)
    @ExcelProperty(value = "(initiate mode)启用状态",converter = IntegerDictsConverter.class)
    @Schema(description="启用状态")
    private Integer isValid;

    /**
     * 创建人
     */
    @ColumnWidth(15)
    @ExcelProperty("(creator)创建人")
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ColumnWidth(15)
    @ExcelProperty("(Create time)创建时间")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    /**
     * 更新人
     */
    @ColumnWidth(15)
    @ExcelProperty("(Updater)更新人")
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ColumnWidth(15)
    @ExcelProperty("(turnover time)更新时间")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;





}
