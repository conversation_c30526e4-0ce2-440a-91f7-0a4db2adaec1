package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsOrderBatchDto;
import com.jygjexp.jynx.tms.entity.TmsOrderBatchEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsOrderBatchService;
import com.jygjexp.jynx.tms.vo.TmsOrderBatchPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 批次管理
 *
 * <AUTHOR>
 * @date 2025-05-13 15:19:16
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsOrderBatch" )
@Tag(description = "tmsOrderBatch" , name = "批次管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOrderBatchController {

    private final  TmsOrderBatchService tmsOrderBatchService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 批次管理
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderBatch_view')" )
    public R getTmsOrderBatchPage(@ParameterObject Page page, @ParameterObject TmsOrderBatchPageVo vo) {
        return R.ok(tmsOrderBatchService.search(page, vo));
    }


    /**
     * 通过id查询批次管理
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderBatch_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsOrderBatchService.getById(id));
    }

    @Operation(summary = "根据批次号查询跟踪单详情列表" , description = "根据批次号查询跟踪单详情列表" )
    @GetMapping("/detail/{batchNo}")
    public R getDetailByBatchNo(@ParameterObject Page page, @PathVariable("batchNo") String batchNo) {
        if (StrUtil.isBlank(batchNo)) {
            return R.failed("batchNo not null");
        }
        return R.ok(tmsOrderBatchService.getDetailByBatchNo(page, batchNo));
    }

    /**
     * 新增批次管理
     * @param tmsOrderBatch 批次管理
     * @return R
     */
    @Operation(summary = "新增批次管理" , description = "新增批次管理" )
    @SysLog("新增批次管理" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderBatch_add')" )
    public R save(@RequestBody TmsOrderBatchEntity tmsOrderBatch) {
        return R.ok(tmsOrderBatchService.save(tmsOrderBatch));
    }

    /**
     * 修改批次管理
     * @param tmsOrderBatch 批次管理
     * @return R
     */
    @Operation(summary = "修改批次管理", description = "修改批次管理")
    @SysLog("修改批次管理")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderBatch_edit')")
    public R updateById(@RequestBody TmsOrderBatchEntity tmsOrderBatch) {
        // 查询批次号在系统中是否存在
        TmsOrderBatchEntity batch = tmsOrderBatchService.getOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, tmsOrderBatch.getBatchNo())
                .ne(TmsOrderBatchEntity::getId, tmsOrderBatch.getId()));
        if (Objects.nonNull(batch)) {
            return LocalizedR.failed("tms.batchno.repeat", "");
        }
        // 先查询出原来批次号
        TmsOrderBatchEntity orderBatch = tmsOrderBatchService.getById(tmsOrderBatch.getId());
        String oldBatchNo = orderBatch.getBatchNo();
        // 修改批次号，并同步修改与其绑定的客户订单的批次号
        return R.ok(tmsOrderBatchService.updateBatchNo(tmsOrderBatch, oldBatchNo));
    }

    /**
     * 通过id删除批次管理
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除批次管理" , description = "通过id删除批次管理" )
    @SysLog("通过id删除批次管理" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderBatch_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsOrderBatchService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 创建空批次
     * @param dto
     * @return
     */
    @Operation(summary = "创建空批次" , description = "创建空批次" )
    @SysLog("创建空批次" )
    @PostMapping("/createOrderBatch")
    @PreAuthorize("@pms.hasPermission('tms_orderBatch_create')" )
    public R addOrderBatch(@RequestBody TmsOrderBatchDto dto) {
        return R.ok(tmsOrderBatchService.createOrderBatch(dto));
    }

    // 查询全部已创建批次
    @Operation(summary = "查询全部已创建批次", description = "查询全部已创建批次")
    @GetMapping("/list")
    public R list() {
        LambdaQueryWrapper<TmsOrderBatchEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(TmsOrderBatchEntity::getCreateTime); // 按创建时间倒序排序
        return R.ok(tmsOrderBatchService.list(queryWrapper));
    }

    /**
     * 导出excel 表格
     * @param tmsOrderBatch 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderBatch_export')" )
    public List<TmsOrderBatchEntity> export(TmsOrderBatchEntity tmsOrderBatch,Long[] ids) {
        return tmsOrderBatchService.list(Wrappers.lambdaQuery(tmsOrderBatch)
                .in(ArrayUtil.isNotEmpty(ids), TmsOrderBatchEntity::getId, ids)
                .orderByDesc(TmsOrderBatchEntity::getId));
    }

    /**
     * 开启或关闭扫描
     */
    @Operation(summary = "开启或关闭扫描" , description = "开启或关闭扫描" )
    @SysLog("开启或关闭批次扫描" )
    @PostMapping("/openingScan")
//    @PreAuthorize("@pms.hasPermission('tms_orderBatch_openingScan')" )
    public R openingScan(@RequestBody TmsOrderBatchEntity tmsOrderBatch) {
        LambdaUpdateWrapper<TmsOrderBatchEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq((tmsOrderBatch.getId() != null), TmsOrderBatchEntity::getId, tmsOrderBatch.getId())
                .set((tmsOrderBatch.getIsScaning() != null), TmsOrderBatchEntity::getIsScaning, tmsOrderBatch.getIsScaning());
        return R.ok(tmsOrderBatchService.update(wrapper));
    }

    /**
     * 获取所有开启的批次
     */
    @Operation(summary = "获取所有开启的批次" , description = "获取所有开启的批次" )
    @SysLog("获取所有开启的批次")
    @GetMapping("/listEnableScanBatch")
//    @PreAuthorize("@pms.hasPermission('tms_orderBatch_listEnableScanBatch')" )
    public R listEnableScanBatch() {
        LambdaQueryWrapper<TmsOrderBatchEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsOrderBatchEntity::getIsScaning, 1);
        // 倒序
        wrapper.orderByDesc(TmsOrderBatchEntity::getCreateTime);
        return R.ok(tmsOrderBatchService.list(wrapper));
    }
}