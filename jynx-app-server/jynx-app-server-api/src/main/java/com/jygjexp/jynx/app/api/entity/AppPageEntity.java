package com.jygjexp.jynx.app.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 页面
 *
 * <AUTHOR>
 * @date 2023-06-08 11:19:23
 */
@Data
@TenantTable
@TableName("app_page")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "页面")
public class AppPageEntity extends Model<AppPageEntity> {

	/**
	 * 主键
	 */
	@TableId(type = IdType.ASSIGN_ID)
	@Schema(description = "主键")
	private Long id;

	/**
	 * 页面类型
	 */
	@Schema(description = "页面类型")
	private Integer pageType;

	/**
	 * 页面名称
	 */
	@Schema(description = "页面名称")
	private String pageName;

	/**
	 * 页面数据
	 */
	@Schema(description = "页面数据")
	private String pageData;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 修改人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@Schema(description = "修改人")
	private String updateBy;

	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@Schema(description = "修改时间")
	private LocalDateTime updateTime;

	/**
	 * 删除标记
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "删除标记")
	private String delFlag;

}
