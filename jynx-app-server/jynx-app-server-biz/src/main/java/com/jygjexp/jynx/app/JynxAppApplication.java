package com.jygjexp.jynx.app;

import com.jygjexp.jynx.common.feign.annotation.EnableJynxFeignClients;
import com.jygjexp.jynx.common.security.annotation.EnableJynxResourceServer;
import com.jygjexp.jynx.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> archetype
 * <p>
 * 项目启动类
 */
@EnableOpenApi("app")
@EnableJynxFeignClients
@EnableDiscoveryClient
@EnableJynxResourceServer
@SpringBootApplication
public class JynxAppApplication {

	public static void main(String[] args) {
		SpringApplication.run(JynxAppApplication.class, args);
	}

}
