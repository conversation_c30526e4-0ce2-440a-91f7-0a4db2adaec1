/*
 *    Copyright (c) 2018-2025, jynx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jynx
 */

package com.jygjexp.jynx.app.mapper;

import com.jygjexp.jynx.app.api.entity.AppRole;
import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * app角色表
 *
 * <AUTHOR>
 * @date 2022-12-07 09:52:03
 */
@Mapper
public interface AppRoleMapper extends JynxBaseMapper<AppRole> {

	List<AppRole> listRolesByUserId(Long userId);

}
